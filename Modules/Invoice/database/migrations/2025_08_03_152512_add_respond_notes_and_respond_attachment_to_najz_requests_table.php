<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('najz_requests', function (Blueprint $table) {
            $table->text('respond_notes')->nullable()->after('status');
            $table->string('respond_attachment')->nullable()->after('respond_notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('najz_requests', function (Blueprint $table) {
            $table->dropColumn(['respond_notes', 'respond_attachment']);
        });
    }
};
