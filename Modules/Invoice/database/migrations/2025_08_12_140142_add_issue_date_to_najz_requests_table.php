<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1️⃣ Add the column as nullable first
        Schema::table('najz_requests', function (Blueprint $table) {
            $table->date('issue_date')->nullable()->after('attachment');
        });

        // 2️⃣ Fill old rows with created_at date
        DB::table('najz_requests')->update([
            'issue_date' => DB::raw('DATE(created_at)')
        ]);

        // 3️⃣ Make it NOT NULL
        Schema::table('najz_requests', function (Blueprint $table) {
            $table->date('issue_date')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('najz_requests', function (Blueprint $table) {
            $table->dropColumn('issue_date');
        });
    }
};
