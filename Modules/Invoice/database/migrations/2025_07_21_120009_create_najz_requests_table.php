<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('najz_requests', function (Blueprint $table) {
            $table->id();
            $table->string('najz_request_id')->unique(); 
            $table->foreignId('invoice_id')->constrained('invoices')->onDelete('cascade');
            $table->morphs('submitted_by');
            $table->enum('status', ['submitted', 'approved', 'rejected'])->default('submitted');
            $table->timestamps(0);
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('najz_requests');
    }
};