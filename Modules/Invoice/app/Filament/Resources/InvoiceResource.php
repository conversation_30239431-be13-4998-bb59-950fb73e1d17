<?php

namespace Modules\Invoice\app\Filament\Resources;

use Modules\Invoice\app\Filament\Resources\InvoiceResource\Components\FormComponent;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers\LineItemsRelationManager;
use Modules\Invoice\app\Models\Invoice;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers\InvoicePaymentsRelationManager;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers\NajzRequestRelationManager;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Widgets\InvoiceListOverview;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema(FormComponent::getForm());
    }

    public static function table(Table $table): Table
    {
        $list = FormComponent::getList();

        return $table
            ->columns($list['columns'])
            ->filters($list['filters'])
            ->actions($list['actions'])
            ->bulkActions($list['bulkActions']);
    }

    public static function getRelations(): array
    {
        return [
            LineItemsRelationManager::class,
            InvoicePaymentsRelationManager::class,
            NajzRequestRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}/show'),
        ];
    }
    public static function getWidgets(): array
    {
        return [
            InvoiceListOverview::class,
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Invoice');
    }


    public static function getNavigationLabel(): string
    {
        return __("Invoices");
    }

    public static function getBreadcrumb() : string
    {
        return __('Invoice');
    }
    public static function getModelLabel(): string
    {
        return __('Invoice');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Invoice');
    }
}
