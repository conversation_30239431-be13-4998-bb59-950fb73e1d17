<?php

namespace Modules\Invoice\app\Filament\Resources\NajzRequestResource\Actions;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Modules\Invoice\app\Models\NajzRequest;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\Enums\NajzStatusEnum;
use Modules\Invoice\Services\NajzRequestService;

class NajzResponceAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'NajzRequest';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('Respond'))
            ->modalHeading(__('Request Najz'))
            ->modalSubmitActionLabel(__('Submit Request Najz'))
            ->icon('heroicon-o-cog')
            ->color('success')
            ->visible(fn () => $this->getRecord()->status == NajzStatusEnum::SUBMITTED->value)
            ->form([
                Section::make(__('Respond'))
                    ->schema([
                        TextInput::make('respond_notes')
                            ->label(__('Notes')),
                        Select::make('status')
                            ->options([
                                'approved' => __('Approved'),
                                'rejected' => __('Rejected'),
                            ])
                            ->required()
                            ->label(__('Status'))
                            ->required(),
                        FileUpload::make('icon')
                            ->label(__('Attachment'))
                            ->directory('najz-attachments')
                            ->required()
                            ->image()
                            ->imageEditor()
                            ->maxSize(5120)
                    ]),
            ])
            ->action(function (array $data): void {
                $najzService = new NajzRequestService();
                $najzService->notifyUpdatedNajzLeaseMembers($this->record , $data['status']);
                $this->record->status = $data['status'];
                $this->record->respond_notes = $data['respond_notes'];
                $this->record->respond_attachment = $data['icon'] ? $data['icon'] : null;
                $this->record->save();  
                
                Notification::make()
                    ->title(__('Najz Request Creation Successful'))
                    ->success()
                    ->send();
            });
    }

    public function getRecord(): ?Model
    {
        return $this->record;
    }
}