<?php

namespace Modules\Invoice\app\Filament\Resources\NajzRequestResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource;
use Modules\Invoice\Services\NajzRequestService;

class CreateNajzRequest extends CreateRecord
{
    protected static string $resource = NajzRequestResource::class;
    
    public function afterCreate()
    {
        $najzReauestService = new NajzRequestService();
        $najzReauestService->notifyLeaseMembers($this->record->invoice);   
    }
}
