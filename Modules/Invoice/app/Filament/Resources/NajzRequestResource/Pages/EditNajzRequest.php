<?php

namespace Modules\Invoice\app\Filament\Resources\NajzRequestResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource;

class EditNajzRequest extends EditRecord
{
    protected static string $resource = NajzRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
