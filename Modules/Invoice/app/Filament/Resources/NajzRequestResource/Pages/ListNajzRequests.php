<?php

namespace Modules\Invoice\app\Filament\Resources\NajzRequestResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource;

class ListNajzRequests extends ListRecords
{
    protected static string $resource = NajzRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
