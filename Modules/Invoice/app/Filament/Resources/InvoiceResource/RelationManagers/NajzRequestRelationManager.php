<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\RelationManagers;

use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Invoice\Enums\NajzStatusEnum;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource\Actions\NajzResponceAction;

class NajzRequestRelationManager extends RelationManager
{
    protected static string $relationship = 'najzRequests';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Najz Requests');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Najz Requests');
    }
    
    public function form(Form $form): Form
    {
        return NajzRequestResource::form($form);
    }
    
    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('najz_request_id')
                    ->label(__('Najz Request Number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('creator')
                    ->label(__('Creator'))
                    ->getStateUsing(fn ($record) => $record->creator->name ?? '')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated At'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options([
                        NajzStatusEnum::SUBMITTED->value => __('Submitted'),
                        NajzStatusEnum::APPROVED->value => __('Approved'),
                        NajzStatusEnum::REJECTED->value => __('Rejected'),
                    ])
                    ->attribute('status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                ->url(fn (Model $record) => route('filament.admin.resources.najz-requests.view', ['record' => $record])),
                NajzResponceAction::make(),
            ]);
    }
}
