<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Actions;

use App\Forms\Components\HijriDatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\Enums\NajzStatusEnum;
use Illuminate\Support\HtmlString;
use Modules\Invoice\Services\NajzRequestService;

class NajzRequestAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'NajzRequest';
    }

    protected function setUp(): void
    {
        parent::setUp();

            $this->label
                (new HtmlString(
                    '<span class="flex items-center">
                        <img src="' . asset('images/najz_logo.svg') . '" alt="Najz Logo" class="w-8 h-8 ">
                        <span style="min-width: 50px;text-align: start;">
                        ' . __('Najz') . '
                        </span>
                    </span>'
                ))
            ->modalHeading(__('Request Najz'))
            ->modalSubmitActionLabel(__('Submit Request Najz'))
            ->color('success')
            ->visible(function ($record) {

                if (!$record || $record->status === InvoiceStatusEnum::PAID) {
                    return false;
                }

                return !$record->najzRequest()
                    ->whereIn('status', [NajzStatusEnum::SUBMITTED->value, NajzStatusEnum::APPROVED->value])
                    ->exists();            
            })
            ->form([
                Section::make(__('Attachment'))
                    ->schema([
                        TextInput::make('najz_request_id')
                            ->label(__('Najz Request Number'))
                            ->required(),
                        Hidden::make('invoice_id')
                            ->label(__('Invoice Id'))
                            ->default(fn() => $this->record->id),
                        HijriDatePicker::make('issue_date')
                            ->label(__('Issue Date'))
                            ->default(today()) // default to current date
                            ->required(),
                        Hidden::make('submitted_by_id')
                            ->label(__('Invoice Id'))
                            ->default(fn() => auth()->id()),
                        Hidden::make('submitted_by_type')
                            ->label(__('Invoice Id'))
                            ->default(fn() => auth()->user()::class),
                        FileUpload::make('icon')
                            ->label(__('Attachment'))
                            ->directory('najz-attachments')
                            ->image()
                            ->imageEditor()
                            ->maxSize(5120)
                    ]),
            ])
            ->action(function (array $data): void {
                $data['attachment'] = $data['icon'] ? $data['icon'] : null;
                $this->record->najzRequest()->create($data);
                $najzReauestService = new NajzRequestService();
                $najzReauestService->notifyLeaseMembers($this->record);
                Notification::make()
                    ->title(__('Najz Request Creation Successful'))
                    ->success()
                    ->send();
            });
    }

    public function getRecord(): ?Model
    {
        return $this->record;
    }
}