<?php

namespace Modules\Invoice\app\Filament\Resources;

use App\Forms\Components\HijriDatePicker;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Modules\Invoice\app\Models\Invoice;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables\Columns\ImageColumn;
use Filament\Forms\Components\FileUpload;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource\Actions\NajzResponceAction;
use Modules\Invoice\Enums\NajzStatusEnum;
use Modules\Invoice\app\Models\NajzRequest;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource\Pages\EditNajzRequest;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource\Pages\ListNajzRequests;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource\Pages\ViewNajzRequests;
use Modules\Invoice\app\Filament\Resources\NajzRequestResource\Pages\CreateNajzRequest;

class NajzRequestResource extends Resource
{
    protected static ?string $model = NajzRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('najz_request_id')
                    ->label(__('Najz Request Number'))
                    ->unique('najz_requests' , 'najz_request_id')
                    ->required(),

                Select::make('invoice_id')
                    ->label(__('Invoice'))
                    ->relationship('invoice', 'id')
                    ->options(
                        function ($record,$livewire) {
                            if($livewire instanceof ViewRecord) {
                                return Invoice::query()
                                ->where('status', '!=', InvoiceStatusEnum::PAID)
                                ->pluck('id', 'id');
                            };
                            return Invoice::query()
                                ->where('status', '!=', InvoiceStatusEnum::PAID)
                                ->whereDoesntHave('najzRequests', function ($query) {
                                    $query->whereIn('status', [
                                        NajzStatusEnum::SUBMITTED->value,
                                        NajzStatusEnum::APPROVED->value,
                                    ]);
                                })
                                ->pluck('id', 'id');
                        } 
                    
                    )
                    ->required(),
                HijriDatePicker::make('issue_date')
                    ->label(__('Issue Date'))
                    ->required(),

                Hidden::make('submitted_by_type')
                    ->default(fn() => auth()->user()::class)
                    ->label(__('Submitted By Type'))
                    ->required(),

                Hidden::make('submitted_by_id')
                    ->default(fn() => auth()->user()->id)
                    ->required(),
                
                TextInput::make('respond_notes')
                    ->label(__('ٌResponce Notes'))
                    ->visible(fn($record) => $record && $record->status !== NajzStatusEnum::SUBMITTED->value)
                    ->required(),

                FileUpload::make('respond_attachment')
                    ->label(__('Respond Attachment'))
                    ->visible(fn($record) => $record && $record->status !== NajzStatusEnum::SUBMITTED->value)
                    ->directory('najz-attachments')
                    ->image()
                    ->imageEditor()
                    ->maxSize(5120),

                FileUpload::make('attachment')
                    ->label(__('Attachment'))
                    ->default(fn ($record) => $record ? $record->attachment : null)
                    ->maxSize(5120)
                    ->preserveFilenames()
                    ->downloadable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('najz_request_id')
                    ->label(__('Najz Request Number'))
                    ->searchable(),

                TextColumn::make('invoice_id')
                    ->label(__('Invoice ID')),

                TextColumn::make('creator')
                    ->label(__('Creator'))
                    ->getStateUsing(fn ($record) => $record->creator->name ?? '')
                    ->searchable(),

                TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->colors([
                        'primary' => 'pending',
                        'success' => 'approved',
                        'danger' => 'rejected',
                    ]),

                ImageColumn::make('attachment')
                    ->getStateUsing(function ($record) {
                        return $record->attachment ? asset('storage' . '/' . $record->attachment) : null;
                    })
                        ->width(100),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime(),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                NajzResponceAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListNajzRequests::route('/'),
            'create' => CreateNajzRequest::route('/create'),
            'edit' => EditNajzRequest::route('/{record}/edit'),
            'view' => ViewNajzRequests::route('/{record}'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Invoice');
    }

    public static function getNavigationLabel(): string
    {
        return __("Najz Requests");
    }

    public static function getBreadcrumb() : string
    {
        return __('Najz Requests');
    }

    public static function getModelLabel(): string
    {
        return __('Najz Request');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Najz Requests');
    }
}
