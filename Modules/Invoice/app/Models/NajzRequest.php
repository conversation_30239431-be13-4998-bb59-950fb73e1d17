<?php

namespace Modules\Invoice\app\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;

class NajzRequest extends BaseModel
{
    use SoftDeletes;

    protected $table = 'najz_requests';

    /**
     * The attributes that are not mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    protected static function boot()
    {
        parent::boot();
    }

    /**
     * Get the model that the invoice is for (polymorphic relation).
     */
    public function creator()
    {
        return $this->morphTo('submitted_by');
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class , 'invoice_id');
    }
}
