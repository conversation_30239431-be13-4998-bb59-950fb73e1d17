<?php

namespace Modules\Invoice\app\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Invoice\app\Models\NajzRequest;

class NajzRequestPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_najz::request');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, NajzRequest $najzRequest): bool
    {
        return $user->can('view_najz::request');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_najz::request');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, NajzRequest $najzRequest): bool
    {
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, NajzRequest $najzRequest): bool
    {
        return $user->can('delete_najz::request');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_najz::request');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, NajzRequest $najzRequest): bool
    {
        return $user->can('force_delete_najz::request');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_najz::request');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, NajzRequest $najzRequest): bool
    {
        return $user->can('restore_najz::request');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_najz::request');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, NajzRequest $najzRequest): bool
    {
        return $user->can('replicate_najz::request');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_najz::request');
    }
}
