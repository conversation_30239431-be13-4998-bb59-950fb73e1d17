<?php

namespace Modules\Invoice\Enums;

enum NajzStatusEnum: string
{
    case SUBMITTED = 'submitted';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';

    public static function getStatusOptions(): array
    {
        return [
            self::SUBMITTED->value => __('Submitted'),
            self::APPROVED->value => __('Approved'),
            self::REJECTED->value => __('Rejected'),
        ];
    }

    public static function getStatusValues(): array
    {
        return [
            self::SUBMITTED->value,
            self::APPROVED->value,
            self::REJECTED->value,
        ];
    }

    public static function getLabel(string $status): string
    {
        return match($status) {
            self::SUBMITTED->value => __('Submitted'),
            self::APPROVED->value => __('Approved'),
            self::REJECTED->value => __('Rejected'),
            default => __('Unknown'),
        };
    }

    public function getColor(): string
    {
        return match($this) {
            self::SUBMITTED => 'warning',
            self::APPROVED => 'success',
            self::REJECTED => 'danger',
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::SUBMITTED => 'heroicon-o-clock',
            self::APPROVED => 'heroicon-o-check-circle',
            self::REJECTED => 'heroicon-o-x-circle',
        };
    }
}
