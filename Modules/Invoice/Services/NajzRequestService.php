<?php

namespace Modules\Invoice\Services;

use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Account\app\Models\Account;
use Modules\Lease\app\Models\LeaseMember;

class NajzRequestService
{
    public function notifyLeaseMembers($invoice): void
    {
        $extra = $invoice->extra;
        if($extra !== null)
        {
        $extra = json_decode($extra, true);
        $leaseId = $extra['lease_id'] ?? null;
        $members = LeaseMember::where('lease_id' , $leaseId)->where('member_type' , 'individual')->get()->pluck('member_id');
        $template = NotificationsTemplate::where(['key' => 'najz_request_created'])->first();
        
        foreach($members as $member)
        {
            if ($template) {
                SendNotification::make(['email'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($member)
                    ->findBody(['{invoice_id}'])
                    ->replaceBody([$invoice->id])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
        }   
    }

    public function notifyUpdatedNajzLeaseMembers($request , $status): void
    {
        $invoice = $request->invoice;
        $extra = $invoice->extra;

        if($extra !== null)
        {
        $extra = json_decode($extra, true);
        $leaseId = $extra['lease_id'] ?? null;
        $members = LeaseMember::where('lease_id' , $leaseId)->where('member_type' , 'individual')->get()->pluck('member_id');

        $template = NotificationsTemplate::where(['key' => 'najz_request_updated'])->first();
        
        foreach($members as $member)
        {
            if ($template) {
                SendNotification::make(['email'])
                    ->template($template->key)
                    ->model(Account::class)
                    ->id($member)
                    ->findBody(['{invoice_number}' , '{request_status}'])
                    ->replaceBody([$invoice->id , $status == 'approved' ? __('Approved') : __('Rejected')])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
        }   
    }
}
