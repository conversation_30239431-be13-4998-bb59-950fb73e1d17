<?php

namespace Modules\Lease\Enums;

enum LeaseSettingEnum: string
{
    const AUTO_RENEWAL_NOTICE_PERIOD = 'renewal_notice_period';
    const DAYS_TO_REMIND_BEFORE_RENEWAL = 'days_to_remind_before_renewal';
    const NUMBER_OF_DAYS_AGAO_TO_RENEW_LEASE = 'number_of_days_ago_to_renew_lease';

    public static function getLeaseTypes(): array // New method for values
    {
        return [
            self::AUTO_RENEWAL_NOTICE_PERIOD,
            self::DAYS_TO_REMIND_BEFORE_RENEWAL,
            self::NUMBER_OF_DAYS_AGAO_TO_RENEW_LEASE,
        ];
    }
}
