<?php

namespace Modules\Lease\Enums;

enum LeaseTermsEnum: string
{
    // Commercial Terms
    case SUBLETTING_OR_LEASE_TRANSFER = 'subletting_or_lease_transfer';
    case IS_REVIEWING_GOVERNMENT = 'is_reviewing_government';
    case SAFETY_AND_SECURITY_PRECAUTIONS = 'safety_and_security_precautions';
    case CHANGE_OF_RENTAL_UNIT = 'change_of_rental_unit';
    case DOING_DECORATION_WORK = 'doing_decoration_work';
    case DAILY_VALUE_OF_THE_DELAY = 'daily_value_of_the_delay';
    case IS_PROOF_OF_TRADEMARK_REGISTRATION = 'is_proof_of_trademark_registration';
    case IS_OBTAINING_A_COMPREHENSIVE = 'is_obtaining_a_comprehensive';
    case IS_MAINTAINING_MECHANICAL_AND_ELECTRICAL_EQUIPMENT = 'is_maintaining_mechanical_and_electrical_equipment';
    case IS_ABSTAINING_FROM_SMOKING_IN_RESTRICTED_AREAS = 'is_abstaining_from_smoking_in_restricted_areas';
    case WASTE_REMOVAL = 'waste_removal';
    case REMOVAL_OF_IMPROVEMENTS = 'removal_of_improvements';
    case CANCEL_THE_MUNICIPAL_LICENSE = 'cancel_the_municipal_license';
    case CANCEL_MUNICIPAL_LICENSE_DAILY_VALUE_OF_DELAY = 'cancel_municipal_license_daily_value_of_delay';
    case DAILY_CHARGE_DAILY_VALUE_OF_DELAY = 'daily_charge_daily_value_of_delay';
    case IS_APPROVED_USER_GUIDE = 'is_approved_user_guide';
    case IS_REPLACING_THE_RENTAL_UNIT = 'is_replacing_the_rental_unit';
    case NUMBER_OF_REPORTING_DAYS = 'number_of_reporting_days';

    // Mall/Retail Sector Terms
    case IS_ADHERE_TO_TENANT_GUIDE = 'is_adhere_to_tenant_guide';
    case IS_ADHERENCE_TO_OPENING_AND_CLOSING_DATES = 'is_adherence_to_opening_and_closing_dates';
    case COMMERCIAL_DISCOUNTS = 'commercial_discounts';
    case IS_ISOLATION_OF_FLOOR = 'is_isolation_of_floor';
    case IS_LOADING_AND_UNLOADING_OPERATIONS = 'is_loading_and_unloading_operations';
    case IS_CANCELLATION_OR_TRANSFER_OF_PHONE_LINES = 'is_cancellation_or_transfer_of_phone_lines';

    // Closing Period Terms
    case MAXIMUM_TIME_CLOSED_CONTINUOUSLY = 'maximum_time_closed_continuously';
    case MAXIMUM_TOTAL_TIME_CLOSED_IN_YEAR = 'maximum_total_time_closed_in_year';

    // Opening Period Terms
    case SPECIFY_OPENING_PERIOD_ACTIVITY = 'specify_opening_period_activity';
    case SPECIFY_TERMINATION_UNIT_NOT_OPENED = 'specify_termination_unit_not_opened';

    // Mutual Rights Terms
    case RESPONSIBLE_FOR_PAYING_FEES_SERVICES = 'responsible_for_paying_fees_services';
    case RESPONSIBLE_FOR_PROVIDING_THE_BASIC_REQUIREMENTS = 'responsible_for_providing_the_basic_requirements';
    case IS_VALIDITY_OF_THE_RENTAL_CONTRACT_ON_OWNER_CHANGE = 'is_validity_of_the_rental_contract_on_owner_change';
    case IS_OBLIGATION_TO_PAY_THE_REMAINING_RENTAL_VALUE = 'is_obligation_to_pay_the_remaining_rental_value';

    // General Items Terms
    case EJAR_FEE_BY = 'ejar_fee_by';
    case CONSTRUCTION_COMPLETION = 'construction_completion';
    case CIVIL_DEFENSE = 'civil_defense';
    case ELECTRICITY_SAFETY = 'electricity_safety';
    case IS_CHANGE_COMMERCIAL_ACTIVITY = 'is_change_commercial_activity';

    /**
     * Get the label for the term
     */
    public function getLabel(): string
    {
        return match($this) {
            self::SUBLETTING_OR_LEASE_TRANSFER => __('Subletting or Lease Transfer'),
            self::IS_REVIEWING_GOVERNMENT => __('Review government and official agencies'),
            self::SAFETY_AND_SECURITY_PRECAUTIONS => __('Safety and security precautions'),
            self::CHANGE_OF_RENTAL_UNIT => __('Change of rental unit'),
            self::DOING_DECORATION_WORK => __('Doing decoration work'),
            self::DAILY_VALUE_OF_THE_DELAY => __('Daily value of the delay'),
            self::IS_PROOF_OF_TRADEMARK_REGISTRATION => __('Proof of trademark registration'),
            self::IS_OBTAINING_A_COMPREHENSIVE => __('Obtaining a comprehensive cooperative insurance policy'),
            self::IS_MAINTAINING_MECHANICAL_AND_ELECTRICAL_EQUIPMENT => __('Maintaining Mechanical and Electrical Equipment'),
            self::IS_ABSTAINING_FROM_SMOKING_IN_RESTRICTED_AREAS => __('Abstaining from Smoking in Restricted Areas'),
            self::WASTE_REMOVAL => __('Waste Removal'),
            self::REMOVAL_OF_IMPROVEMENTS => __('Removal of Improvements After the Lease Term Expires'),
            self::CANCEL_THE_MUNICIPAL_LICENSE => __('Cancel the Municipal License'),
            self::CANCEL_MUNICIPAL_LICENSE_DAILY_VALUE_OF_DELAY => __('Daily value of the delay'),
            self::DAILY_CHARGE_DAILY_VALUE_OF_DELAY => __('Daily value of the delay'),
            self::IS_APPROVED_USER_GUIDE => __('Approval of the User Guide'),
            self::IS_REPLACING_THE_RENTAL_UNIT => __('Replacing the rental unit'),
            self::NUMBER_OF_REPORTING_DAYS => __('Number of reporting days'),
            self::IS_ADHERE_TO_TENANT_GUIDE => __('Adhere to the Tenant Guide'),
            self::IS_ADHERENCE_TO_OPENING_AND_CLOSING_DATES => __('Adherence to the opening and closing dates of the rental unit'),
            self::COMMERCIAL_DISCOUNTS => __('Commercial discounts'),
            self::IS_ISOLATION_OF_FLOOR => __('Isolation of the floor for any activity that requires the use of water and sanitation'),
            self::IS_LOADING_AND_UNLOADING_OPERATIONS => __('Loading and unloading operations'),
            self::IS_CANCELLATION_OR_TRANSFER_OF_PHONE_LINES => __('Cancellation or transfer of phone lines'),
            self::MAXIMUM_TIME_CLOSED_CONTINUOUSLY => __('Maximum time closed continuously'),
            self::MAXIMUM_TOTAL_TIME_CLOSED_IN_YEAR => __('Maximum total time closed in a year'),
            self::SPECIFY_OPENING_PERIOD_ACTIVITY => __('Specify opening period for intended activity'),
            self::SPECIFY_TERMINATION_UNIT_NOT_OPENED => __('Specify period for terminating if unit not opened'),
            self::RESPONSIBLE_FOR_PAYING_FEES_SERVICES => __('Responsible for paying fees for the services of the competent authorities'),
            self::RESPONSIBLE_FOR_PROVIDING_THE_BASIC_REQUIREMENTS => __('Responsible for providing the basic requirements for issuing the shop license'),
            self::IS_VALIDITY_OF_THE_RENTAL_CONTRACT_ON_OWNER_CHANGE => __('The validity of the rental contract on owner change'),
            self::IS_OBLIGATION_TO_PAY_THE_REMAINING_RENTAL_VALUE => __('The obligation to pay the remaining rental value'),
            self::EJAR_FEE_BY => __('Ejar Fees Paid By'),
            self::CONSTRUCTION_COMPLETION => __('Construction completion certificate'),
            self::CIVIL_DEFENSE => __('Civil defense requirements'),
            self::ELECTRICITY_SAFETY => __('Electricity Safety Requirements'),
            self::IS_CHANGE_COMMERCIAL_ACTIVITY => __('Change Commercial Activity'),
        };
    }

    /**
     * Get the description for the term
     */
    public function getDescription(): string
    {
        return match($this) {
            self::SUBLETTING_OR_LEASE_TRANSFER => __('The obligations are determined based on choosing one of the following materials'),
            self::IS_REVIEWING_GOVERNMENT => __('The lessee has the right to review the government and official agencies, the authorities concerned with issuing the shop license, and others.'),
            self::SAFETY_AND_SECURITY_PRECAUTIONS => __('The tenant is obligated to take the necessary precautions to prevent exposure to the rental unit and all its accessories and those present in it.'),
            self::CHANGE_OF_RENTAL_UNIT => __('The lessee has the right to change the rental unit, whether by increase, decrease or amendment.'),
            self::DOING_DECORATION_WORK => __('The lessee has the right to carry out decoration works, paintings, facades, electrical loads, or construction works for the rental unit that do not make major changes to the property.'),
            self::DAILY_VALUE_OF_THE_DELAY => __('The tenant is obligated to pay a specified fee daily in case of delay in opening the rental unit after the completion of the procedures The official contracting and.'),
            self::IS_PROOF_OF_TRADEMARK_REGISTRATION => __('The lessee is obligated to provide the lessor with proof of trademark registration or his right or authority to sell the trademark products for the used trade name.'),
            self::IS_OBTAINING_A_COMPREHENSIVE => __('The lessee is obligated to obtain a comprehensive cooperative insurance policy (in accordance with the provisions of Islamic law) for the rental unit and all his possessions, including decor and.'),
            self::IS_MAINTAINING_MECHANICAL_AND_ELECTRICAL_EQUIPMENT => __('The lessee is obligated to maintain all mechanical and electrical equipment, and take into account the electrical loads in the complex and other equipment that the lessor has placed in.'),
            self::IS_ABSTAINING_FROM_SMOKING_IN_RESTRICTED_AREAS => __('The tenant and their employees are prohibited from smoking in the hallways and lobbies of the premises, and are expected to only smoke in designated areas.'),
            self::WASTE_REMOVAL => __('The tenant is obligated to throw the waste in the designated area, and not to throw any solid materials or oils into the sewage streams.'),
            self::REMOVAL_OF_IMPROVEMENTS => __('The obligations are determined based on choosing one of the following materials.'),
            self::CANCEL_THE_MUNICIPAL_LICENSE => __('The tenant is obligated to cancel the municipality\'s license for the rental unit upon the expiration or termination of the contract. The following options apply:'),
            self::CANCEL_MUNICIPAL_LICENSE_DAILY_VALUE_OF_DELAY => __('Daily value of the delay for municipal license cancellation'),
            self::DAILY_CHARGE_DAILY_VALUE_OF_DELAY => __('The lessor is obligated to pay a daily fee in the event that he is late in delivering the rental unit to the tenant after completing the formal contracting procedures.'),
            self::IS_APPROVED_USER_GUIDE => __('The lessee acknowledges receipt and has access to the details and contents of the Tenant Manual and the conditions, specifications.'),
            self::IS_REPLACING_THE_RENTAL_UNIT => __('The right to replace the rental unit with another equivalent unit'),
            self::NUMBER_OF_REPORTING_DAYS => __('The number of days for reporting requirements'),
            self::IS_ADHERE_TO_TENANT_GUIDE => __('Under this contract, the lessor and the lessee have agreed to abide by the Tenant Manual of the property (the commercial center) as a reference guide for them that regulates the tenant\'s.'),
            self::IS_ADHERENCE_TO_OPENING_AND_CLOSING_DATES => __('The tenant is committed to the opening and closing dates of the rental unit mentioned in the Tenant Manual, which is applicable in the commercial center, and in the event of.'),
            self::COMMERCIAL_DISCOUNTS => __('The obligations are determined based on choosing one of the following materials'),
            self::IS_ISOLATION_OF_FLOOR => __('The tenant is obligated to isolate the floor of the rental unit according to the specifications mentioned in the Tenant Manual, in the event that it is used as toilets, a restaurant.'),
            self::IS_LOADING_AND_UNLOADING_OPERATIONS => __('The tenant shall abide by the times and dates mentioned in the Tenant Manual for loading and unloading operations.'),
            self::IS_CANCELLATION_OR_TRANSFER_OF_PHONE_LINES => __('The obligation to cancel or transfer the fixed telephone lines of the rental unit upon the end of the contract.'),
            self::MAXIMUM_TIME_CLOSED_CONTINUOUSLY => __('Maximum time closed continuously (Days)'),
            self::MAXIMUM_TOTAL_TIME_CLOSED_IN_YEAR => __('Maximum total time closed in a year (Days)'),
            self::SPECIFY_OPENING_PERIOD_ACTIVITY => __('The lessee is obligated to open and start his commercial activity within a period not exceeding (days) days from the date of the contract'),
            self::SPECIFY_TERMINATION_UNIT_NOT_OPENED => __('The lessee is obligated to open and start his commercial activity within a period not exceeding (days) days from the date of the contract'),
            self::RESPONSIBLE_FOR_PAYING_FEES_SERVICES => __('Complying with any service fees that may be imposed on rental units by the competent authorities'),
            self::RESPONSIBLE_FOR_PROVIDING_THE_BASIC_REQUIREMENTS => __('Commitment to providing the basic requirements for government and official agencies and all competent authorities with regard to assistance in issuing a shop license.'),
            self::IS_VALIDITY_OF_THE_RENTAL_CONTRACT_ON_OWNER_CHANGE => __('In the event that the ownership of the real estate or rental units is transferred to another owner, the tenant\'s contract will remain and will not be terminated until the end of its term.'),
            self::IS_OBLIGATION_TO_PAY_THE_REMAINING_RENTAL_VALUE => __('The lessor or the lessee is obligated to pay (20%) of the remaining rental value of the lease contract in effect in the desire of either of them to terminate the contract.'),
            self::EJAR_FEE_BY => __('Set who should pay the annual contract registration fee on the Ejar platform:'),
            self::CONSTRUCTION_COMPLETION => __('Issuing a certificate from the Ministry of Municipal and Rural Affairs to complete construction'),
            self::CIVIL_DEFENSE => __('A list of the safety conditions, fire prevention, alarm and extinguishing equipment from the Civil Defense Authority'),
            self::ELECTRICITY_SAFETY => __('A receipt for the safety of electrical panels and switches from the Saudi Electricity Company'),
            self::IS_CHANGE_COMMERCIAL_ACTIVITY => __('The lessee has the right to change the commercial activity registered in the contract after lessor approval.'),
        };
    }

    /**
     * Get options for terms that have multiple choice values
     */
    public function getOptions(): array
    {
        return match($this) {
            self::SUBLETTING_OR_LEASE_TRANSFER => [
                'WholeRental' => __('The lessee has the right to rent the whole rental unit to others'),
                'PartOfRental' => __('The lessee has the right to rent part of the rental unit space to others after obtaining the approval of the lessor'),
                'AssignContract' => __('The sub-tenant has the right to assign the contract to others, in whole or in part, after obtaining the approval of the lessor')
            ],
            self::REMOVAL_OF_IMPROVEMENTS => [
                'RemoveImprovements' => __('The lessee is obligated to hand over the rental units to the lessor upon the end of the lease period of this contract, in the state in which they were received, and remove any new improvements.'),
                'NotRemoveImprovements' => __('The lessee is obligated not to remove the improvements, fixed decor works that cannot be moved, or any additions made to the rental units without obtaining written approval from the lessor or their agent.')
            ],
            self::CANCEL_THE_MUNICIPAL_LICENSE => [
                'CancelTheMunicipalLicense' => __('The tenant is obligated to cancel the municipality\'s license for the rental unit upon expiration or termination of the contract.'),
                'CancelTheMunicipalLicenseAndSetDailyCharges' => __('The tenant is obligated to cancel the municipality\'s license for the rental unit upon expiry or termination of the contract. Otherwise, the tenant shall be deemed bound by a specific daily fee.')
            ],
            self::COMMERCIAL_DISCOUNTS => [
                'NoticeOfLassor' => __('The lessee is obligated not to announce the commercial discounts before notifying the lessor and obtaining the written approval.'),
                'ObtainingRequiredApprovals' => __('The lessee shall not announce commercial discounts until after obtaining the required approvals from.')
            ],
            default => []
        };
    }

    /**
     * Get term definition array (for backward compatibility)
     */
    public function getDefinition(): array
    {
        $definition = [
            'label' => $this->getLabel(),
            'description' => $this->getDescription()
        ];

        $options = $this->getOptions();
        if (!empty($options)) {
            $definition['options'] = $options;
        }

        return $definition;
    }

    /**
     * Get term definition by string key
     */
    public static function getTermDefinition(string $key): array
    {
        $enum = self::tryFrom($key);
        
        if ($enum) {
            return $enum->getDefinition();
        }

        return [
            'label' => $key,
            'description' => ''
        ];
    }
}
