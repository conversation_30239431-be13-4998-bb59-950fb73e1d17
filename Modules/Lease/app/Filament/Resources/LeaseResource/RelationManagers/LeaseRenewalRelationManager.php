<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\RelationManagers;

use Filament\Tables\Table;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;
use Modules\Lease\Enums\LeaseEnum;
use App\Helpers\LeaseSettingHelper;
use Filament\Tables\Actions\Action;
use Modules\Lease\app\Models\Lease;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Modules\Lease\Services\LeaseService;
use Filament\Tables\Actions\DeleteAction;
use Modules\Lease\Enums\LeaseSettingEnum;
use Modules\Lease\Enums\SubLeaseTypeEnum;
use Modules\Request\Services\RequestService;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Lease\app\Filament\Resources\LeaseResource\Actions\PublishLeaseTableAction;

class LeaseRenewalRelationManager extends RelationManager
{
    protected static string $relationship = 'leaseRenewals';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Lease Renewals');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Lease Renewals');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('lease_version')
                    ->label(__('Version'))
                    ->getStateUsing(function ($record){
                        $originalLeaseId = $record->parent_id;
                        
                        return $originalLeaseId . ' - V' . $record->lease_version;
                    }),

                TextColumn::make('property.name')
                    ->label(__('Property')),
    
                TextColumn::make('start_date')
                    ->label(__('Start Date'))
                    ->dateTime('Y-m-d'), // Format as a readable date
    
                TextColumn::make('end_date')
                    ->label(__('End Date'))
                    ->dateTime('Y-m-d'),

                TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (string $state): string => LeaseEnum::getColor($state))
                    ->formatStateUsing(fn (string $state): string => LeaseEnum::trans($state)),
                TextColumn::make('rent_amount')
                    ->label(__('Rent Amount'))
                    ->formatStateUsing(function ($state) {
                        return
                        new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    }),
                IconColumn::make('auto_renewal')
                    ->label(__('Auto Renewal Status'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle') // Right mark
                    ->falseIcon('heroicon-o-x-circle')    // Wrong mark
                    ->getStateUsing(fn ($record) => $record->auto_renewal === 'on'),
    
                TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                ViewAction::make()
                    ->url(fn (Lease $record): string => LeaseResource::getUrl('view', ['record' => $record])),
                DeleteAction::make()
                    ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::DRAFT),
                PublishLeaseTableAction::make(),
                Action::make(__('terminate'))->color('danger')->icon('heroicon-o-x-circle')
                    ->visible(fn (Lease $record): bool => 
                        in_array($record->status, [LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE]) 
                        && $record->auto_renewal !== LeaseAutoRenewalEnum::PENDING)
                    ->url(fn (Lease $record): string => LeaseResource::getUrl('terminate', ['record' => $record])),
                Action::make(__('close'))
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::ENDED)
                    ->url(fn (Lease $record): string => LeaseResource::getUrl('close', ['record' => $record])),
                Action::make(__('Auto Renew Request'))
                    ->label(fn($record)=> $record->status === LeaseEnum::ENDED ? __('Renew') : __('Auto Renew Request'))
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->visible(fn (Lease $record): bool => $record->auto_renewal === LeaseAutoRenewalEnum::OFF 
                        && in_array($record->status, [LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE, LeaseEnum::Renewed,LeaseEnum::ENDED]))                    
                    ->requiresConfirmation()
                    ->modalHeading(__('Auto Renew Request'))
                    ->modalDescription(fn (Lease $record): string => LeaseService::getAutoRenewalRequestModal($record))
                    ->modalSubmitActionLabel(__('Yes, proceed'))
                    ->modalCancelActionLabel(__('Cancel'))
                    ->action(function (Lease $record) {
                        try {
                            $service = new RequestService();
                            $service->handleAutoRenewalRequest($record);

                            Notification::make()
                                ->title(__('Auto renewal request sent successfully'))
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title(__('Error'))
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
                    Action::make(__('Cancel Auto Renew'))
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->visible(fn (Lease $record): bool => 
                        $record->auto_renewal === LeaseAutoRenewalEnum::ON 
                        && in_array($record->status, [LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE]) 
                        && Carbon::parse($record->end_date)
                            ->subDays(LeaseSettingHelper::getLeaseSettingValue(LeaseSettingEnum::AUTO_RENEWAL_NOTICE_PERIOD))
                            ->format('Y-m-d') > now()->format('Y-m-d') // Ensure correct date format comparison
                    )
                    ->requiresConfirmation()
                    ->modalHeading(__('Cancel Auto Renew'))
                    ->modalDescription(fn (Lease $record): string => LeaseService::getCancelAutoRenewalModal($record))
                    ->modalSubmitActionLabel(__('Yes, proceed'))
                    ->modalCancelActionLabel(__('Cancel'))
                    ->action(function (Lease $record) {
                        try {
                            LeaseService::cancelAutoRenewal($record->id);

                            Notification::make()
                                ->title(__('Auto renewal cancellation sent successfully'))
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title(__('Error'))
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
                Action::make('contract')
                    ->label(__('Contract'))
                    ->icon('heroicon-o-document-text')
                    ->url(fn (Lease $record): string => LeaseResource::getUrl('contract', ['record' => $record]))
                    ->openUrlInNewTab(),
            ]);
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord?->sub_lease_type != SubLeaseTypeEnum::RENEWED && $ownerRecord?->leaseRenewals()->count() > 0;
    }

    public function isReadOnly(): bool
    {
        return false;
    }


}
