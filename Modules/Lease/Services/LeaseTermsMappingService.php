<?php

namespace Modules\Lease\Services;

use Modules\Lease\Enums\LeaseTermsEnum;

class LeaseTermsMappingService
{
    /**
     * Get term definition by key using enum
     */
    public static function getTermDefinition(string $key): array
    {
        return LeaseTermsEnum::getTermDefinition($key);
    }

    /**
     * Get commercial terms definitions (for backward compatibility)
     */
    public static function getCommercialTermsDefinitions(): array
    {
        $terms = [];
        $commercialTerms = [
            LeaseTermsEnum::SUBLETTING_OR_LEASE_TRANSFER,
            LeaseTermsEnum::IS_REVIEWING_GOVERNMENT,
            LeaseTermsEnum::SAFETY_AND_SECURITY_PRECAUTIONS,
            LeaseTermsEnum::CHANGE_OF_RENTAL_UNIT,
            LeaseTermsEnum::DOING_DECORATION_WORK,
            LeaseTermsEnum::DAILY_VALUE_OF_THE_DELAY,
            LeaseTermsEnum::IS_PROOF_OF_TRADEMARK_REGISTRATION,
            LeaseTermsEnum::IS_OBTAINING_A_COMPREHENSIVE,
            LeaseTermsEnum::IS_MAINTAINING_MECHANICAL_AND_ELECTRICAL_EQUIPMENT,
            LeaseTermsEnum::IS_ABSTAINING_FROM_SMOKING_IN_RESTRICTED_AREAS,
            LeaseTermsEnum::WASTE_REMOVAL,
            LeaseTermsEnum::REMOVAL_OF_IMPROVEMENTS,
            LeaseTermsEnum::CANCEL_THE_MUNICIPAL_LICENSE,
            LeaseTermsEnum::CANCEL_MUNICIPAL_LICENSE_DAILY_VALUE_OF_DELAY,
            LeaseTermsEnum::DAILY_CHARGE_DAILY_VALUE_OF_DELAY,
            LeaseTermsEnum::IS_APPROVED_USER_GUIDE,
            LeaseTermsEnum::IS_REPLACING_THE_RENTAL_UNIT,
            LeaseTermsEnum::NUMBER_OF_REPORTING_DAYS,
            LeaseTermsEnum::IS_ADHERE_TO_TENANT_GUIDE,
            LeaseTermsEnum::IS_ADHERENCE_TO_OPENING_AND_CLOSING_DATES,
            LeaseTermsEnum::COMMERCIAL_DISCOUNTS,
            LeaseTermsEnum::IS_ISOLATION_OF_FLOOR,
            LeaseTermsEnum::IS_LOADING_AND_UNLOADING_OPERATIONS,
            LeaseTermsEnum::IS_CANCELLATION_OR_TRANSFER_OF_PHONE_LINES,
        ];

        foreach ($commercialTerms as $term) {
            $terms[$term->value] = $term->getDefinition();
        }

        return $terms;
    }

    /**
     * Get mutual rights terms definitions (for backward compatibility)
     */
    public static function getMutualRightsDefinitions(): array
    {
        $terms = [];
        $mutualRightsTerms = [
            LeaseTermsEnum::RESPONSIBLE_FOR_PAYING_FEES_SERVICES,
            LeaseTermsEnum::RESPONSIBLE_FOR_PROVIDING_THE_BASIC_REQUIREMENTS,
            LeaseTermsEnum::IS_VALIDITY_OF_THE_RENTAL_CONTRACT_ON_OWNER_CHANGE,
            LeaseTermsEnum::IS_OBLIGATION_TO_PAY_THE_REMAINING_RENTAL_VALUE,
        ];

        foreach ($mutualRightsTerms as $term) {
            $terms[$term->value] = $term->getDefinition();
        }

        return $terms;
    }

    /**
     * Get general items terms definitions (for backward compatibility)
     */
    public static function getGeneralItemsDefinitions(): array
    {
        $terms = [];
        $generalItemsTerms = [
            LeaseTermsEnum::EJAR_FEE_BY,
            LeaseTermsEnum::CONSTRUCTION_COMPLETION,
            LeaseTermsEnum::CIVIL_DEFENSE,
            LeaseTermsEnum::ELECTRICITY_SAFETY,
            LeaseTermsEnum::IS_CHANGE_COMMERCIAL_ACTIVITY,
        ];

        foreach ($generalItemsTerms as $term) {
            $terms[$term->value] = $term->getDefinition();
        }

        return $terms;
    }

    /**
     * Get closing period terms definitions (for backward compatibility)
     */
    public static function getClosingPeriodDefinitions(): array
    {
        $terms = [];
        $closingPeriodTerms = [
            LeaseTermsEnum::MAXIMUM_TIME_CLOSED_CONTINUOUSLY,
            LeaseTermsEnum::MAXIMUM_TOTAL_TIME_CLOSED_IN_YEAR,
        ];

        foreach ($closingPeriodTerms as $term) {
            $terms[$term->value] = $term->getDefinition();
        }

        return $terms;
    }

    /**
     * Get opening period terms definitions (for backward compatibility)
     */
    public static function getOpeningPeriodDefinitions(): array
    {
        $terms = [];
        $openingPeriodTerms = [
            LeaseTermsEnum::SPECIFY_OPENING_PERIOD_ACTIVITY,
            LeaseTermsEnum::SPECIFY_TERMINATION_UNIT_NOT_OPENED,
        ];

        foreach ($openingPeriodTerms as $term) {
            $terms[$term->value] = $term->getDefinition();
        }

        return $terms;
    }
}
