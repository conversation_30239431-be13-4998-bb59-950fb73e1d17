<div class="pt-3 overflow-x-auto">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('date')}}
                </th>
                <th scope="col" class="px-6 py-3 whitespace-nowrap">
                    {{__('financial equivalent')}}
                </th>
            </tr>
        </thead>
        <tbody>
            <tr class="border-b border-b-gray-500 bg-white dark:bg-gray-800">
                <td class="px-6 py-4 whitespace-nowrap">
                    @if(isset($this->data['start_scheduled_date']))
                        {{ \Carbon\Carbon::parse($this->data['start_scheduled_date'])->format('d-m-Y') }}
                    @elseif(isset($start_scheduled_date))
                        {{ \Carbon\Carbon::parse($start_scheduled_date)->format('d-m-Y') }}
                    @endif
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    125 <span class="icon-saudi_riyal"></span>
                    <span class="bg-[#1a5848] px-1 mx-1 text-white">
                        0%
                    </span>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="border-b border-b-gray-500 bg-white dark:bg-gray-800">
                <td class="px-6 py-4 whitespace-nowrap">
                    {{__('Total')}}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    125 <span class="icon-saudi_riyal"></span>
                </td>
            </tr>
        </tfoot>
    </table>
</div>
