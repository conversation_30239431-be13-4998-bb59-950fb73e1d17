<div class="space-y-6 lg:max-h-[500px] lg:overflow-auto">
    <h2 class="text-xl font-bold dark:text-white">{{ __('Residential Terms and Conditions') }}</h2>

    @if(isset($termsAndConditions) && count($termsAndConditions) > 0)
        @php
            $additionalTerms = collect($termsAndConditions)->filter(function($term) {
                return $term['key'] === 'additional_terms';
            })->values();

            $regularTerms = collect($termsAndConditions)->filter(function($term) {
                return $term['key'] !== 'additional_terms';
            });
        @endphp

        <!-- Payment of the fee by -->
        @php
            $feeByTerm = $regularTerms->firstWhere('key', 'fee_by');
        @endphp
        @if($feeByTerm)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold dark:text-white">{{ __('Payment of the fee by') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('Pay the financial fee to document the contract through') }}
                </p>
                <div class="mt-3 flex justify-end">
                    <span class="bg-primary-400/20 dark:bg-primary-900/20 border border-primary-800 dark:border-primary-800 rounded-lg px-3 py-1">
                        <span class="text-primary-800 dark:text-primary-200 font-medium">{{ $feeByTerm['value'] === 'MediationOffice' ? __('Mediation office') : __('The lessor') }}</span>
                    </span>
                </div>
                @include('lease::forms.components.terms-table',['start_scheduled_date' => $start_scheduled_date])
            </div>
        @endif

        <!-- Sublease -->
        @php
            $subleaseTerm = $regularTerms->firstWhere('key', 'is_sublease');
        @endphp
        @if($subleaseTerm)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold dark:text-white">{{ __('Sublease') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('The tenant is permitted to sublease this property with a new sublease') }}
                </p>
                <div class="mt-3 flex justify-end">
                    <span class="{{ $subleaseTerm['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ $subleaseTerm['value'] ? __('Yes') : __('No') }}
                    </span>
                </div>
            </div>
        @endif

        <!-- Reviewing government and official agencies -->
        @php
            $reviewingGovTerm = $regularTerms->firstWhere('key', 'is_reviewing_government');
        @endphp
        @if($reviewingGovTerm)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold dark:text-white">{{ __('Reviewing government and official agencies') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('The tenant has the right to review governmental and official agencies and concerned authorities regarding the rented real estate unit') }}
                </p>
                <div class="mt-3 flex justify-end">
                    <span class="{{ $reviewingGovTerm['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ $reviewingGovTerm['value'] ? __('Yes') : __('No') }}
                    </span>
                </div>
            </div>
        @endif

        <!-- Repairs and improvements to the rental unit -->
        @php
            $repairsTerm = $regularTerms->firstWhere('key', 'is_repairs_improvements');
        @endphp
        @if($repairsTerm)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold dark:text-white">{{ __('Repairs and improvements to the rental unit') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('The tenant has the right to make repairs and improvements to the rental unit that do not cause fundamental changes to the property') }}
                </p>
                <div class="mt-3 flex justify-end">
                    <span class="{{ $repairsTerm['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ $repairsTerm['value'] ? __('Yes') : __('No') }}
                    </span>
                </div>
            </div>
        @endif

        <!-- Modify the rental unit -->
        @php
            $modifyTerm = $regularTerms->firstWhere('key', 'is_modify_rental_unit');
        @endphp
        @if($modifyTerm)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold dark:text-white">{{ __('Modify the rental unit') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('The tenant has the right to modify the rental unit, whether by increasing, decreasing or amending it') }}
                </p>
                <div class="mt-3 flex justify-end">
                    <span class="{{ $modifyTerm['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        {{ $modifyTerm['value'] ? __('Yes') : __('No') }}
                    </span>
                </div>
            </div>
        @endif

        <!-- Governing Law and Dispute Resolution -->
        @php
            $governingLawTerm = $regularTerms->firstWhere('key', 'governing_law');
        @endphp
        @if($governingLawTerm)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold dark:text-white">{{ __('Governing Law and Dispute Resolution') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('Except for the rights and obligations that are subject to the jurisdiction of the Enforcement Court in accordance with the Enforcement System, any dispute or disagreement arising') }}
                </p>
                <div class="mt-3">
                    <div class="bg-primary-400/20 dark:bg-primary-900/20 border border-primary-800 dark:border-primary-800 rounded-lg p-3">
                        <span class="text-primary-800 dark:text-primary-200 font-medium">
                            @if($governingLawTerm['value'] === 'SaudiCenter')
                                {{ __('Saudi Center for Real Estate Arbitration in accordance with the Center\'s procedural rules') }}
                            @else
                                {{ __('The competent judicial authority in the Kingdom of Saudi Arabia') }}
                            @endif
                        </span>
                    </div>
                </div>
            </div>
        @endif

        <!-- Other regular terms -->
        @foreach($regularTerms as $term)
            @if(!in_array($term['key'], ['fee_by', 'is_sublease', 'is_reviewing_government', 'is_repairs_improvements', 'is_modify_rental_unit', 'governing_law']))
                <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                    <h4 class="font-semibold dark:text-white">{{ __(Str::title(str_replace(['is_', '_'], ['', ' '], $term['key']))) }}</h4>
                    <div class="mt-3 flex justify-end">
                        @if(is_bool($term['value']))
                            <span class="{{ $term['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                {{ $term['value'] ? __('Yes') : __('No') }}
                            </span>
                        @else
                            <span class="dark:text-gray-300">{{ $term['value'] }}</span>
                        @endif
                    </div>
                </div>
            @endif
        @endforeach

        <!-- Additional Terms and Conditions Section -->
        @if($additionalTerms->count() > 0)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-3 border border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold dark:text-white">{{ __('Additional Terms and Conditions') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('With the inclusion of additional terms and conditions. The contract will remain in effect as an executory instrument') }}
                </p>
                <div class="mt-4 space-y-3">
                    @foreach($additionalTerms as $index => $term)
                        <div class="flex items-start space-x-3">
                            <span class="inline-flex items-center justify-center w-6 h-6 bg-primary-500 text-white text-sm font-medium rounded-full flex-shrink-0">
                                {{ $index + 1 }}
                            </span>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm dark:text-gray-300">{{ $term['value'] }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    @else
        <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <p class="dark:text-gray-300">{{ __('No terms and conditions available') }}</p>
        </div>
    @endif
</div>
