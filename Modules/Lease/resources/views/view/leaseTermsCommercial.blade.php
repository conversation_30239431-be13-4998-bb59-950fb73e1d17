@php
    use Modules\Lease\Services\LeaseTermsMappingService;
@endphp

<div class="space-y-6 lg:max-h-[500px] lg:overflow-auto">
    <h2 class="text-xl font-bold dark:text-white">{{ __('Commercial Terms and Conditions') }}</h2>

    @if(isset($termsAndConditions) && count($termsAndConditions) > 0)
        @php
            // Separate additional terms from regular terms
            $additionalTerms = collect($termsAndConditions)->filter(function($term) {
                return $term['key'] === 'additional_terms';
            })->values();

            // Get contract type
            $contractTypeTerms = collect($termsAndConditions)->where('key', 'contract_type')->first();

            // Get conditions_checkbox items (excluding null values)
            $conditionsCheckboxTerms = collect($termsAndConditions)->filter(function($term) {
                $extraData = json_decode($term['extra_data'] ?? '{}', true);
                return isset($extraData['array_key']) &&
                    $extraData['array_key'] === 'generalItems' &&
                    $term['key'] === 'conditions_checkbox' &&
                    $term['value'] !== null && $term['value'] !== '';
            });

            // Get tenant and lessor terms (excluding null values)
            $tenantLessorTerms = collect($termsAndConditions)->filter(function($term) {
                $extraData = json_decode($term['extra_data'] ?? '{}', true);
                return ($extraData['array_key'] ?? '') === 'tenantAndLessor' &&
                       $term['value'] !== null && $term['value'] !== '';
            });

            // Get general items terms (excluding null values)
            $generalItemsTerms = collect($termsAndConditions)->filter(function($term) {
                $extraData = json_decode($term['extra_data'] ?? '{}', true);
                return ($extraData['array_key'] ?? '') === 'generalItems' &&
                       $term['key'] !== 'additional_terms' &&
                       $term['key'] !== 'conditions_checkbox' &&
                       $term['value'] !== null && $term['value'] !== '';
            });
        @endphp

        <!-- Contract Type Section -->
        @if($contractTypeTerms)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-3">
                <h4 class="font-semibold dark:text-white">{{ __('Contract Type') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('Terms and conditions to which the contracting will be subject') }}
                </p>

                <div class="mt-4">
                    @php
                        $contractTypeOptions = [
                            'UnifiedContract' => [
                                'label' => __('Unified contract'),
                                'description' => __('A unified contract that allows its parties to easily view and select multiple types of obligations while preserving the executive nature of the document.')
                            ],
                            'OpenContract' => [
                                'label' => __('Open contract (non-standardized)'),
                                'description' => __('A contract that allows its parties to formulate its clauses and obligations according to the agreement during the contract, including determining amicable periods for resolving the dispute and determining the court.')
                            ]
                        ];
                        $selectedOption = $contractTypeOptions[$contractTypeTerms['value']] ?? null;
                    @endphp

                    @if($selectedOption)
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h5 class="font-medium dark:text-white">{{ $selectedOption['label'] }}</h5>
                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $selectedOption['description'] }}</p>
                        </div>
                    @endif
                </div>

            </div>
        @endif

        <!-- Tenant and Lessor Commitments Section -->
        @if($tenantLessorTerms->count() > 0)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-3">
                <h4 class="font-semibold dark:text-white">{{ __('Tenant and Lessor Commitments') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('Rights and obligations for both tenant and lessor') }}
                </p>

                <div class="mt-4 space-y-4">
                    @php
                        // Group terms by their group property
                        $groupedTerms = $tenantLessorTerms->groupBy(function($term) {
                            $termDefinitions = [
                                'maximum_time_closed_continuously' => ['group' => 'closing_period'],
                                'maximum_total_time_closed_in_year' => ['group' => 'closing_period'],
                                'specify_opening_period_activity' => ['group' => 'opening_period'],
                                'specify_termination_unit_not_opened' => ['group' => 'opening_period']
                            ];

                            $termDef = $termDefinitions[$term['key']] ?? ['group' => 'individual'];
                            return $termDef['group'] ?? 'individual';
                        });
                    @endphp

                    @foreach($groupedTerms as $groupKey => $groupTerms)
                        @if($groupKey === 'closing_period')
                            <!-- Grouped Closing Period Terms -->
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <h5 class="font-medium dark:text-white">{{ __('Determine the Period of Closing the Rental Unit') }}</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                    {{ __('The lessee is obligated not to close the rental unit for the inventory or others for more than a specific period continuously or for more than a specified period intermittently throughout.') }}
                                </p>

                                <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                                    @foreach($groupTerms as $term)
                                        @php
                                            $termDef = LeaseTermsMappingService::getTermDefinition($term['key']);
                                        @endphp
                                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                            <div class="text-sm text-gray-600 dark:text-gray-300">{{ $termDef['label'] }}</div>
                                            <div class="text-lg font-medium dark:text-white mt-1">{{ $term['value'] }} {{ __('Days') }}</div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @elseif($groupKey === 'opening_period')
                            <!-- Grouped Opening Period Terms -->
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <h5 class="font-medium dark:text-white">{{ __('Unit Opening and Unit Termination Periods') }}</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                    {{ __('The lessee is obligated to open and start his commercial activity within a period not exceeding (days) days from the date of the contract:') }}
                                </p>

                                <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                                    @foreach($groupTerms as $term)
                                        @php
                                            $termDef = LeaseTermsMappingService::getTermDefinition($term['key']);
                                        @endphp
                                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                            <div class="text-sm text-gray-600 dark:text-gray-300">{{ $termDef['label'] }}</div>
                                            <div class="text-lg font-medium dark:text-white mt-1">{{ $term['value'] }} {{ __('Days') }}</div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            @foreach($groupTerms as $term)
                                @php
                                    $termDef = LeaseTermsMappingService::getTermDefinition($term['key']);
                                @endphp

                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                    <h5 class="font-medium dark:text-white">{{ $termDef['label'] }}</h5>
                                    @if($termDef['description'])
                                        <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $termDef['description'] }}</p>
                                    @endif

                                    <div class="mt-3">
                                        @if(isset($termDef['options']) && isset($termDef['options'][$term['value']]))
                                            <div class="bg-primary-400/20 dark:bg-primary-900/20 border border-primary-800 dark:border-primary-800 rounded-lg p-3">
                                                <span class="text-primary-800 dark:text-primary-200 font-medium">{{ $termDef['options'][$term['value']] }}</span>
                                            </div>
                                        @elseif(is_bool($term['value']) || in_array($term['value'], ['0', '1', 0, 1]))
                                            <div class="flex justify-end">
                                                <span class="{{ $term['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                                    {{ $term['value'] ? __('Yes') : __('No') }}
                                                </span>
                                            </div>
                                        @else
                                            <div class="flex justify-end">
                                                @if(is_numeric($term['value']) && in_array($term['key'], ['daily_value_of_the_delay', 'cancel_municipal_license_daily_value_of_delay', 'daily_charge_daily_value_of_delay']))
                                                    <span class="dark:text-gray-300">
                                                        {{ $term['value'] }} <span class="icon-saudi_riyal"></span>
                                                    </span>
                                                @elseif(is_numeric($term['value']) && in_array($term['key'], ['number_of_reporting_days']))
                                                    <span class="dark:text-gray-300">
                                                        {{ $term['value'] }} {{ __('Days') }}
                                                    </span>
                                                @else
                                                    <span class="dark:text-gray-300">{{ $term['value'] }}</span>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                </div>
                             @endforeach
                         @endif
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Mutual Rights and Commitments Section -->
        @php
            $mutualRightsTerms = collect($termsAndConditions)->filter(function($term) {
                $extraData = json_decode($term['extra_data'] ?? '{}', true);
                return isset($extraData['array_key']) && $extraData['array_key'] === 'mutualRights' && !is_null($term['value']) && $term['value'] !== '';
            });
        @endphp

        @if($mutualRightsTerms->count() > 0)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md mb-3">
                <h4 class="font-semibold dark:text-white">{{ __('Mutual rights and commitments') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('The party that bears the following obligations is determined by choosing between the tenant and the lessor.') }}
                </p>

                <div class="mt-4 space-y-4">
                    @foreach($mutualRightsTerms as $term)
                        @php
                            $termDef = LeaseTermsMappingService::getTermDefinition($term['key']);
                        @endphp

                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h5 class="font-medium dark:text-white">{{ $termDef['label'] }}</h5>
                            @if($termDef['description'])
                                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $termDef['description'] }}</p>
                            @endif

                            <div class="mt-3 flex justify-end">
                                @if(in_array($term['key'], ['responsible_for_paying_fees_services', 'responsible_for_providing_the_basic_requirements']))
                                    <span class="bg-primary-400/20 dark:bg-primary-900/20 border border-primary-800 dark:border-primary-800 rounded-lg px-3 py-1">
                                        <span class="text-primary-800 dark:text-primary-200  font-medium">{{ $term['value'] === 'lessor' ? __('Lessor') : __('Tenant') }}</span>
                                    </span>
                                @elseif(is_bool($term['value']) || in_array($term['value'], ['0', '1', 0, 1]))
                                    <span class="{{ $term['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ $term['value'] ? __('Yes') : __('No') }}
                                    </span>
                                @else
                                    <span class="dark:text-gray-300">{{ $term['value'] }}</span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- General Items Section -->
        @if($generalItemsTerms->count() > 0)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-3">
                <h4 class="font-semibold dark:text-white">{{ __('General Items') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('General terms and conditions for the commercial lease') }}
                </p>

                <div class="mt-4 space-y-4">
                    @foreach($generalItemsTerms as $term)
                        @php
                            $termDef = LeaseTermsMappingService::getTermDefinition($term['key']);
                        @endphp

                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h5 class="font-medium dark:text-white">{{ $termDef['label'] }}</h5>
                            @if($termDef['description'])
                                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $termDef['description'] }}</p>
                            @endif

                            <div class="mt-3 flex justify-end">
                                @if($term['key'] === 'ejar_fee_by')
                                    <span class="bg-primary-400/20 dark:bg-primary-900/20 border border-primary-800 dark:border-primary-800 rounded-lg px-3 py-1">
                                        <span class="text-primary-800 dark:text-primary-200 font-medium">{{ $term['value'] === 'BrokerageOffice' ? __('Brokerage Office') : __('Lessor') }}</span>
                                    </span>
                                @elseif(is_bool($term['value']) || in_array($term['value'], ['0', '1', 0, 1]))
                                    <span class="{{ $term['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ $term['value'] ? __('Yes') : __('No') }}
                                    </span>
                                @else
                                    <span class="dark:text-gray-300">{{ $term['value'] }}</span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Conditions Checkbox Section -->
        @if($conditionsCheckboxTerms->count() > 0)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-3">
                <h4 class="font-semibold dark:text-white">{{ __('Custom Conditions') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('Custom conditions for this commercial lease') }}
                </p>

                <div class="mt-4 space-y-3">
                    @foreach($conditionsCheckboxTerms as $term)
                        @php
                            $extraData = json_decode($term['extra_data'] ?? '{}', true);
                            $label = $extraData['label'][app()->getLocale()] ?? Str::title(str_replace('_', ' ', $term['key']));
                            $description = $extraData['description'][app()->getLocale()] ?? '';
                        @endphp
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h5 class="font-medium dark:text-white">{{ $label }}</h5>
                            @if($description)
                                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ $description }}</p>
                            @endif
                            <div class="flex justify-end mt-2">
                                <span class="{{ $term['value'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                    {{ $term['value'] ? __('Yes') : __('No') }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Additional Terms and Conditions Section -->
        @if($additionalTerms->count() > 0)
            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-3">
                <h4 class="font-semibold dark:text-white">{{ __('Additional Terms and Conditions') }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {{ __('By adding additional terms and conditions, the contract will not lose the status of an executive document.') }}
                </p>

                <div class="mt-4 space-y-3">
                    @foreach($additionalTerms as $index => $term)
                        <div class="flex items-start space-x-3">
                            <span class="inline-flex items-center justify-center w-6 h-6 bg-primary-500 text-white text-sm font-medium rounded-full flex-shrink-0">
                                {{ $index + 1 }}
                            </span>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm dark:text-gray-300">{{ $term['value'] }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif


    @else
        <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <p class="dark:text-gray-300">{{ __('No terms and conditions available') }}</p>
        </div>
    @endif
</div>
