<?php

namespace Modules\Lease\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\Invoice\app\Models\Invoice;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\app\Models\LeaseUnit;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Modules\Lease\Enums\LeaseSettingEnum;
use Modules\Property\Enums\PropertyStatus;

class LeaseRepository extends RepositoriesAbstract
{
    protected $leaseUnit;
    protected $leaseSettingRepository;
    public function __construct(Lease $model, LeaseUnit $leaseUnit , LeaseSettingRepository $leaseSettingRepository)
    {
        $this->leaseUnit = $leaseUnit;
        $this->leaseSettingRepository = $leaseSettingRepository;
        parent::__construct($model);
    }

    public function getAllByMember(array $condition = [], array $with = [], array $select = ['*']): LengthAwarePaginator
    {
        $query = $this->model::query()->with($with)->where($condition)
            ->where('status', '!=', LeaseEnum::DRAFT);
        $query = $this->applyFilters($query)->select($select);
        return $query->paginate(request()->limit ?? 10);
    }

    public function getUnitServices(array $condition = [], array $select = ['*'], array $with = [])
    {
        $this->model = $this->leaseUnit;
        return $this->getFirstBy($condition, $select, $with);
    }

    public function getLeaseUnit(int $id ,array $condition = [], array $select = ['*'], array $with = [])
    {
        $this->model = $this->leaseUnit;
        return $this->findOrFail($id, $condition, $select, $with);
    }


    public function updateEndedLeases()
    {

        $leasesIds = $this->getEndedLease();

        if(count($leasesIds)){
            $this->model->whereIn('id',$leasesIds) ->update([
                'status'=>LeaseEnum::ENDED
            ]);
        }

        return $leasesIds;
    }

    public function updateReservedLeases()
    {
        $leasesIds = $this->getReservedLeases();

        if(count($leasesIds)){
            $this->model->whereIn('id',$leasesIds) ->update([
                'status'=>LeaseEnum::PUBLISHED
            ]);
        }

        return $leasesIds;
    }

    public function updateNearExpireLeases()
    {
        $leasesIds = $this->getNearExpireLeases();

        if(count($leasesIds)){
            $this->model->whereIn('id',$leasesIds) ->update([
                'status'=>LeaseEnum::NEAR_EXPIRE
            ]);
        }

        return $leasesIds;
    }

    public function getAutoRenwalLeases()
    {
        $daysToRenew = $this->leaseSettingRepository->getFirstBy(['key' => LeaseSettingEnum::NUMBER_OF_DAYS_AGAO_TO_RENEW_LEASE]);
        $leases = $this->model
        ->whereBetween('end_date',[date('Y-m-d',strtotime("-" .$daysToRenew->value ."days")),date('Y-m-d')])
        ->whereNotIn('status',[LeaseEnum::CLOSED, LeaseEnum::Renewed , LeaseEnum::DRAFT,LeaseEnum::TERMINATED])->where('auto_renewal' , LeaseAutoRenewalEnum::ON)
        ->whereHas('leaseUnits.unit', function ($query) {
            $query->where(['status'=>PropertyStatus::ACTIVE,'is_active'=>1]);
        })
        ->get();
        return $leases;
    }

    public function getAutoRenwalLeasesReminders()
    {
        $daysToRemind = $this->leaseSettingRepository->getFirstBy(['key' => LeaseSettingEnum::DAYS_TO_REMIND_BEFORE_RENEWAL]);
        return $this->model->whereDate('end_date',date('Y-m-d',strtotime("-" .$daysToRemind->value ."days")))
            ->whereNotIn('status',[LeaseEnum::ENDED , LeaseEnum::DRAFT,LeaseEnum::TERMINATED])->where('auto_renewal' , LeaseAutoRenewalEnum::ON)
            ->get();
    }

    private function getEndedLease()
    {
        return $this->model->select('id')->whereDate('end_date', '<', today())
            ->whereIn('status',[LeaseEnum::PUBLISHED ,LeaseEnum::NEAR_EXPIRE ,LeaseEnum::TERMINATE_REQUEST])
            ->get()->pluck('id')->toArray();
    }

    private function getReservedLeases()
    {
        return $this->model->select('id')
            ->whereDate('start_date', today())->where('status', LeaseEnum::RESERVED)
            ->pluck('id')->toArray();
    }

    private function getNearExpireLeases()
    {
        return $this->model->select('id')
            ->whereDate('end_date', '<=', today()->addDays(60)) // Less than or equal to 60 days from today
            ->where('status', LeaseEnum::PUBLISHED)
            ->pluck('id')
            ->toArray();
    }

    private function applyFilters(Builder $query)
    {
        //add search filtering
        if (request()->filled('search_key') && strlen(request()->search_key) >= 3) {
            $search = request()->get('search_key');
            $query->where(function($query) use ($search) {
                $query->where('lease_number', 'LIKE', "%{$search}%")
                    ->orWhere('ejar_uuid', 'LIKE', "%{$search}%")
                    ->orWhereHas('leaseUnits', function($query) use ($search) {
                            $query->WhereHas('unit', function($query) use ($search) {
                                $query->where('number', 'LIKE', "%{$search}%");
                            });
                    })
                    ->orWhereHas('leaseMembers', function($query) use ($search) {
                        $query->whereIn('member_role', [LeaseMemberTypesEnum::TENANT, LeaseMemberTypesEnum::TENANT_REPRESENTER])
                            ->whereHas('member', function($query) use ($search) {
                                $query->where('name', 'LIKE', "%{$search}%")
                                    ->orWhere('national_id', 'LIKE', "%{$search}%");
                            });
                    });
            });
        }
        if (request()->has('status')) {
            if (request()->get('status') == 'active') {
                $query->whereIn('status', LeaseEnum::getActiveStatus());
            } elseif (request()->get('status') == 'in_active') {
                $query->whereNotIn('status', LeaseEnum::getActiveStatus());
            }
        }
        if (request()->has('from')) {
            $query->whereDate('start_date', '>=', request()->get('from'));
        }
        if (request()->has('to')) {
            $query->whereDate('end_date', '<=', request()->get('to'));
        }

        return $query;
    }

    public function deleteLease(Lease $lease)
    {
        DB::transaction(function () use ($lease) {
            // Delete related records

            $lease->leaseUnits->each(function ($leaseUnit) {
                if ($leaseUnit->AllleaseUnitServices()->exists()) {
                    $leaseUnit->AllleaseUnitServices()->delete();
                }

                $leaseUnit->delete();
                $leaseUnit->unit->update(['status' => PropertyStatus::ACTIVE]);
            });

            $lease->leaseMembers()->delete();
            $lease->leaseServices()->delete();
            $lease->commission()->delete();
            $lease->documents()->delete();

            $invoices = Invoice::whereJsonContains('extra', ['lease_id' => $lease->id])->get();
            $invoices->each(function ($leaseInvoice) {
                if ($leaseInvoice->allPayments()->exists()) {
                    $leaseInvoice->allPayments()->delete();
                }

                if ($leaseInvoice->items()->exists()) {
                    $leaseInvoice->items()->delete();
                }
                $leaseInvoice->delete();
            });

            $lease->invoiceSchedules()->delete();
            $lease->leaseTerms()->delete();
            $lease->commercial_meta_data()->delete();

            // Delete the lease
            $lease->delete();
        });
    }
}
