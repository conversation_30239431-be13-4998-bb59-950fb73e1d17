<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, make sure existing NULL values are set to 'off'
        DB::table('leases')
            ->whereNull('auto_renewal')
            ->update(['auto_renewal' => 'off']);

        Schema::table('leases', function (Blueprint $table) {
            $table->enum('auto_renewal', ['on', 'off', 'pending'])
                ->default('off')
                ->nullable(false)
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leases', function (Blueprint $table) {
            $table->enum('auto_renewal', ['on', 'off', 'pending'])
                ->nullable()
                ->default(null)
                ->change();
        });
    }
};
