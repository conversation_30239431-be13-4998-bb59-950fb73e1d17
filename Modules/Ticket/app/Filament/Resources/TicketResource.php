<?php

namespace Modules\Ticket\app\Filament\Resources;

use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Modules\Ticket\app\Filament\Resources\TicketResource\Components\FormComponent;
use Modules\Ticket\app\Filament\Resources\TicketResource\Components\TableComponent;
use Modules\Ticket\app\Filament\Resources\TicketResource\Pages\CreateTicket;
use Modules\Ticket\app\Filament\Resources\TicketResource\Pages\ListTickets;
use Modules\Ticket\app\Filament\Resources\TicketResource\Pages\ViewTicket;
use Modules\Ticket\app\Models\Ticket;

class TicketResource extends Resource
{
    protected static ?string $model = Ticket::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema(FormComponent::getForm());
    }

    public static function table(Table $table): Table
    {
        return TableComponent::getTable($table);
    }

    public static function getRelations(): array
    {
        return [
            TicketResource\RelationManagers\MessagesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTickets::route('/'),
            'create' => CreateTicket::route('/create'),
            'view' => ViewTicket::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        if (auth()->user()->hasRole('super_admin')) {
            return parent::getEloquentQuery();
        }
        $query = parent::getEloquentQuery()
            ->where('account_type', auth()->user()::class)
            ->where('account_id', auth()->id());
        return $query;
    }

    public static function getNavigationLabel(): string
    {
        return __("tickets");
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Support Tickets');
    }
    public static function getBreadcrumb() : string
    {
        return __('Ticket');
    }
    public static function getModelLabel(): string
    {
        return __('Ticket');
    }

    public static function getPluralModelLabel(): string
    {
        return __('tickets');
    }
}
