<?php
namespace Modules\Ticket\app\Filament\Resources\TicketResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\ViewField;

class MessagesRelationManager extends RelationManager
{
    protected static string $relationship = 'ticket_messages';
    const SENDER_TYPE = 'App/Models/User';

    public function isReadOnly(): bool { return false; }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Hidden::make('sender_type')
                    ->default(auth()->user()::class)
                    ->required(),
                Hidden::make('sender_id')
                    ->default(auth()->user()->id)
                    ->required(),
                Forms\Components\Textarea::make('message')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Section::make('Attachment')
                    ->schema([
                        Forms\Components\SpatieMediaLibraryFileUpload::make('attachment')
                            ->collection('ticket_messages')
                            ->maxSize(5120)
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->acceptedFileTypes(['image/*', 'application/pdf']),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('messages')
            ->columns([
                Tables\Columns\TextColumn::make('sender.name')
                ->visible(fn() => auth()->user()->hasRole('super_admin')),
                Tables\Columns\TextColumn::make('message'),
                Tables\Columns\TextColumn::make('created_at')
                ->label(__('Created At')),
                Tables\Columns\TextColumn::make('updated_at')
                ->label(__('Updated At')),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->form([
                        Tabs::make('Ticket Message')
                            ->tabs([
                                Tabs\Tab::make('Message Details')
                                    ->schema([
                                        Forms\Components\Textarea::make('message')
                                            ->disabled()
                                            ->columnSpanFull(),
                                    ]),
                                Tabs\Tab::make('Attachments')
                                    ->schema([
                                        ViewField::make('attachments')
                                            ->view('ticket::components.media-viewer')
                                            ->extraAttributes([
                                                'class' => 'filament-modal-prevent-close'
                                            ])
                                            ->columnSpanFull(),
                                    ]),
                            ])
                            ->columnSpanFull()
                            ->extraAttributes([
                                'class' => 'filament-modal-prevent-close'
                            ]),
                    ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }
}
