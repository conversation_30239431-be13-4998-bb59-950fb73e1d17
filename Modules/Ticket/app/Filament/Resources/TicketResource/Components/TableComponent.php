<?php

namespace Modules\Ticket\app\Filament\Resources\TicketResource\Components;

use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\SelectColumn;
use Modules\Ticket\Enums\TicketStatusEnum;
use Modules\Ticket\Enums\TicketPriorityEnum;
use Modules\Ticket\Services\TicketService;
use App\Models\User;
use Modules\Account\app\Models\Account;

class TableComponent
{
    public static function getTable($table)
    {
        $ticketService = app(TicketService::class);
        return $table
            ->columns([
                TextColumn::make(name: 'uuid')
                    ->label(__('Ticket Number'))
                    ->searchable(),

                TextColumn::make('account.name')
                    ->label(__('Sender'))
                    ->sortable()
                    ->searchable()
                    ->url(function ($record) {

                        if ($record->account_type == Account::class) {
                            return \Modules\Account\app\Filament\Resources\AccountsResource::getUrl('view', ['record' => $record->account]);
                        }

                        if ($record->account_type == User::class) {
                            return \app\Filament\Resources\UserResource::getUrl('view', ['record' => $record->account]);
                        }

                        return null;
                    })
                    ->openUrlInNewTab(),

                SelectColumn::make('user_id')
                    ->label(__('Assigned To'))
                    ->visible(fn() => auth()->user()->hasRole('super_admin'))
                    ->options($ticketService->getUserNamesAndIds())
                    ->searchable()
                    ->afterStateUpdated(function ($state, $record) use ($ticketService) {
                        if ($state) {
                            $ticketService->notifyAssignedUser($record);
                        }
                    }),
                TextColumn::make('ticketCategory.name')
                    ->label(__('Category'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('title')
                    ->label(__('Title'))
                    ->searchable(),

                SelectColumn::make('priority')
                    ->label(__('Priority'))
                    ->visible(fn() => auth()->user()->hasRole('super_admin'))
                    ->options(
                        TicketPriorityEnum::getTicketPriorityOptions()
                    )
                    ->searchable(),

                SelectColumn::make('status')
                    ->label(__('Status'))
                    ->disabled(fn() => !auth()->user()->hasRole('super_admin'))
                    ->options(
                        TicketStatusEnum::getTicketStatusOptions()
                    )
                    ->searchable()
                    ->afterStateUpdated(function ($state, $record) use ($ticketService) {
                        if ($state == TicketStatusEnum::CLOSED) {
                            $ticketService->notifyAfterCloseTicket($record);
                        }
                    }),

                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label(__('Updated At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                \Filament\Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(\Modules\Ticket\Enums\TicketStatusEnum::getTicketStatusOptions())
                    ->attribute('status')
                    ->default(null)
                    ->searchable(),
            ])
            ->actions([
                ViewAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                ]),
            ]);
    }
}
