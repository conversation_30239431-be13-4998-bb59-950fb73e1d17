<?php

namespace Modules\Ticket\app\Filament\Resources\TicketResource\Components;


use App\Models\User;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Modules\Account\app\Models\Account;
use Modules\Ticket\app\Models\TicketCategory;
use Modules\Ticket\Enums\TicketPriorityEnum;
use Illuminate\Support\Str;

class FormComponent
{

    public static function getForm() :array{

        return [
            Select::make('account_type')
                ->label(__('User Type'))
                ->required()
                ->visible(fn() => auth()->user()->hasRole('super_admin'))
                ->options([
                    User::class => __('User (Portal User)'),
                    Account::class => __('Account (Mobile User)'),
                ])
                ->reactive()
                ->afterStateUpdated(function (Set $set) {
                    $set('account_id', null);
                }),

            Hidden::make('uuid')
                ->default(fn () => Str::uuid()->toString()),

            Select::make('account_id')
                ->label(__('User Name'))
                ->required()
                ->visible(fn() => auth()->user()->hasRole('super_admin'))
                ->searchable()
                ->placeholder(__('Select an Account'))
                ->reactive()
                ->options(function (callable $get) {
                    $type = $get('account_type');

                    if ($type === User::class) {
                        return User::pluck('name', 'id')->toArray();
                    }

                    if ($type === Account::class) {
                        return Account::pluck('name', 'id')->toArray();
                    }

                    return [];
                }),

            Select::make('user_id')
                ->label(__('Assigned User To Solve The Ticket'))
                ->visible(fn() => auth()->user()->hasRole('super_admin'))
                ->options(User::select('name', 'id')->pluck('name', 'id'))
                ->searchable()
                ->placeholder(__('Select a user')),

            Select::make('ticket_category_id')
                ->label(__('Category')) // Make the label translatable
                ->required()
                ->options(TicketCategory::where('is_active' , 1)->select('name', 'id')->pluck('name', 'id'))
                ->searchable()
                ->placeholder(__('Select a category')), // Make the placeholder translatable

            TextInput::make('title')
                ->label(__('Title'))
                ->required()
                ->maxLength(255),

            Select::make('priority')
                ->label(__('Priority'))
                ->visible(fn() => auth()->user()->hasRole('super_admin'))
                ->required()
                ->options(TicketPriorityEnum::getTicketPriorityOptions())
                ->placeholder(__('Select Priority')),

            Textarea::make('message')
                ->label(__('Content'))
                ->required()
                ->columnSpanFull(),

            Section::make(__('Attachment')) // Make the section label translatable
            ->schema([
                SpatieMediaLibraryFileUpload::make('attachment') // Use Spatie Media Library upload
                ->image()
                ->maxSize(5120)
                    ->imageEditor()
                    ->collection('tickets') // Specify the media collection
                    ->imageEditorAspectRatios([
                        '16:9',
                        '4:3',
                        '1:1',
                    ]),
            ]),
        ];

    }
}
