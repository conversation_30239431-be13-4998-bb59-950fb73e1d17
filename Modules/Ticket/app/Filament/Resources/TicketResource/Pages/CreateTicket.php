<?php

namespace Modules\Ticket\app\Filament\Resources\TicketResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Ticket\app\Filament\Resources\TicketResource;

class CreateTicket extends CreateRecord
{
    protected static string $resource = TicketResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if(!auth()->user()->hasRole('super_admin')){
            $data['account_type'] = auth()->user()::class;
            $data['account_id'] = auth()->id();
        }

        return $data;
    }
}
