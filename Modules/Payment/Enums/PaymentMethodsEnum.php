<?php

namespace Modules\Payment\Enums;

class PaymentMethodsEnum
{
    public const CLICKPAY = 'clickpay';
    public const CASH = 'cash';
    public const BANK_ACCOUNT = 'bank_account';
    public const BANK_TRANSFER = 'bank_transfer';
    public const EJAR = 'ejar';
    public const NAJZ = 'najz';

    public static function labels(): array
    {
        return [
            self::CLICKPAY => __('Clickpay'),
            self::CASH => __('Cash'),
            self::BANK_ACCOUNT => __('Bank Account'),
            self::BANK_TRANSFER => __('Bank Transfer'),
            self::EJAR => __('EJAR'),
            self::NAJZ => __('NAJZ'),
        ];
    }

    public static function getMethodsOptions(): array
    {
        return [
            self::CLICKPAY => __('Clickpay'),
            self::CASH => __('Cash'),
            self::BANK_ACCOUNT => __('Bank Account'),
            self::BANK_TRANSFER => __('Bank Transfer'),
            self::EJAR => __('EJAR'),
            self::NAJZ => __('NAJZ'),
        ];
    }

    public static function getLabel(string $option): string
    {
        if (!$option) {
            return '-';
        }

        return self::labels()[$option];
    }
}
