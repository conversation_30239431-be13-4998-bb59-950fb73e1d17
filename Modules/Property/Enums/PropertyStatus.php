<?php

namespace Modules\Property\Enums;

enum PropertyStatus: string
{

    case DRAFT = 'draft';
    case ACTIVE = 'active';
    case RESERVED = 'reserved';
    case RENTED = 'rented';
    case IN_MAINTENANCE = 'in-maintenance';

    public function label(): string
    {
        return match($this) {
            self::DRAFT => __('Draft'),
            self::ACTIVE => __('Active'),
            self::RESERVED => __('Reserved'),
            self::RENTED => __('Rented'),
            self::IN_MAINTENANCE => __('In-Maintenance'),
        };
    }

    public function getColor(): string
    {
        return match($this) {
            self::DRAFT => 'gray',
            self::ACTIVE => 'success',
            self::RESERVED => 'warning',
            self::RENTED => 'info',
            self::IN_MAINTENANCE => 'danger',
        };
    }

    public static function getOptions(): \Illuminate\Support\Collection
    {
        return collect(self::values())->mapWithKeys(fn ($status) => [$status => __($status)]);
    }

    /**
     * Static method to get label from status
     */
    public static function getLabel(?self $status): string
    {
        if (!$status) {
            return '-';
        }

        return $status->label();
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function labels(): array
    {
        return [
            self::DRAFT->value => __('Draft'),
            self::ACTIVE->value => __('Active'),
            self::RESERVED->value => __('Reserved'),
            self::RENTED->value => __('Rented'),
            self::IN_MAINTENANCE->value => __('In-Maintenance'),
        ];
    }

    public static function getFilterOptions()
    {
        return collect(self::labels())->mapWithKeys(fn ($label, $status) => [$status => $label]);
    }
    
}
