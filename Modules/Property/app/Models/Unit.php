<?php

namespace Modules\Property\app\Models;

use App\Models\User;
use App\Models\Val;
use Khaleds\Shared\Models\BaseModel;
use Modules\Favorite\app\Traits\HasFavorite;
use Modules\Property\Enums\PropertyStatus;
use Modules\Property\app\Models\PropertyMeter;
use Modules\Tenancy\Traits\BelongsToTenancy;
use Modules\Property\app\Models\PropertyType;
use Modules\Property\app\Models\Usability;


/**
 * @property integer $id
 * @property integer $title
 * @property integer $description
 * @property string $link
 * @property string $image
 * @property integer $active
 * @property string $property_id
 */
class Unit extends BaseModel
{
    use HasFavorite;
    use BelongsToTenancy;

    protected $table = "properties";
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $guarded = ['id']; // Guarding the id field

    protected $casts = [
      'status' => PropertyStatus::class
    ];


    public function property()
    {
      return $this->belongsTo(Property::class,"parent_id");
    }

    public function property_type()
    {
      return $this->belongsTo(PropertyType::class);
    }

    public function usability()
    {
      return $this->belongsTo(Usability::class);
    }

    public function attributes()
    {
      return $this->belongsToMany(Attribute::class, 'property_attributes', 'property_id', 'attribute_id');
    }

    public function vals()
    {
      return $this->morphMany(Val::class, 'morphable');
    }

    public function meters()
    {
      return $this->hasOne(PropertyMeter::class,"property_id");
    }

    public function prices()
    {
        return $this->hasMany(UnitPrice::class, 'unit_id');
    }

    public function brokers()
    {
        return $this->belongsToMany(User::class, 'property_brokers', 'property_id', 'broker_id')
            ->withTimestamps()
            ->with('userProfile');
    }
}
