<?php

namespace Modules\Property\app\Filament\Resources\UnitResource\Widgets;

use Modules\Property\app\Models\Property;
use Modules\Property\Enums\PropertyStatus;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class UnitListOverview extends BaseWidget
{
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        $properties = Property::select('status', 'parent_id', 'created_at','updated_at')
            ->get();

        // Total Units (with parent)
        $Units = $properties->whereNotNull('parent_id');
        $totalUnitsChart = $this->calculateDailyStats($Units, 'created_at');
        $totalUnits = $Units->count();
        $this->cards[] = Stat::make(__('Total Units'), $totalUnits)
            ->color('info')
            ->chart(array_reverse($totalUnitsChart));

        // Available Units
        $availableUnitsData = $properties->whereNotNull('parent_id')
        ->where('status', PropertyStatus::ACTIVE);
        $availableUnitsChart = $this->calculateDailyStats($availableUnitsData, 'updated_at' );
        $availableUnits = $availableUnitsData->count();
        $this->cards[] = Stat::make(__('Available Units'), $availableUnits)
            ->color('success')
            ->chart(array_reverse($availableUnitsChart));

        // Rented Units
        $rentedUnitsData = $properties->whereNotNull('parent_id')->where('status', PropertyStatus::RENTED);
        $rentedUnitsChart = $this->calculateDailyStats($rentedUnitsData, 'updated_at');
        $rentedUnits = $rentedUnitsData->count();
        $this->cards[] = Stat::make(__('Rented Units'), $rentedUnits)
            ->color('warning')
            ->chart(array_reverse($rentedUnitsChart));

        return $this->cards;
    }

}

