<?php

namespace Modules\Property\app\Filament\Resources\UnitResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Property\app\Filament\Resources\UnitResource;
use Modules\Property\app\Filament\Resources\PropertyResource\Widgets\PropertyListOverview;
use Modules\Property\app\Filament\Resources\UnitResource\Widgets\UnitListOverview;

class ListUnits extends ListRecords
{
    protected static string $resource = UnitResource::class;

    protected function getHeaderActions(): array
    {
        return [
           
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            UnitListOverview::class
        ];
    }
}
