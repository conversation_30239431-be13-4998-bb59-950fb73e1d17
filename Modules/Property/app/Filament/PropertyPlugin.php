<?php

namespace Modules\Property\app\Filament;

use Coolsam\Modules\Concerns\ModuleFilamentPlugin;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Modules\Property\app\Filament\Resources\AmenitiesCategoryResource;
use Modules\Property\app\Filament\Resources\AmenitiesResource;
use Modules\Property\app\Filament\Resources\AttributeResource;
use Modules\Property\app\Filament\Resources\UsabilityResource;
use Modules\Property\app\Filament\Resources\PropertyResource;
use Modules\Property\app\Filament\Resources\PropertyTypeResource;
use Modules\Property\app\Filament\Resources\UnitResource;

class PropertyPlugin implements Plugin
{
    use ModuleFilamentPlugin;

    public function getModuleName(): string
    {
        return __('Property');
    }

    public function getId(): string
    {
        return 'property';
    }

    public function register(Panel $panel): void
    {
        $panel
            ->resources([
                AmenitiesCategoryResource::class,
                AmenitiesResource::class,
                UsabilityResource::class,
                PropertyTypeResource::class,
                PropertyResource::class,
                AttributeResource::class,
                UnitResource::class,
            ]);
    }

    public function boot(Panel $panel): void
    {
        // TODO: Implement boot() method.
    }


}
