<?php

namespace Modules\Account\app\Filament\Resources\AccountsResource\Widgets;

use Illuminate\Support\Facades\DB;
use Modules\Account\app\Models\Account;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class AccountsOverview extends BaseWidget
{
    private array $cards = [];
    protected static ?string $pollingInterval = '5s';
    use ExposesTableToWidgets;

    /**
     * Get filtered accounts based on the same logic as AccountsResource
     */
    private function getFilteredAccounts()
    {
        $query = Account::query();

        // Apply the same filtering logic as AccountsResource
        $user = auth()->user();
        if ($user && !$user->hasRole('super_admin') && $user->company_id) {
            $query->where(function ($query) use ($user) {
                // Include accounts directly belonging to the company
                $query->where('accounts.company_id', $user->company_id)
                    // OR include accounts that are property owners of properties owned by the company
                    ->orWhereExists(function ($subQuery) use ($user) {
                        $subQuery->select(DB::raw(1))
                            ->from('property_owners')
                            ->join('properties', 'property_owners.property_id', '=', 'properties.id')
                            ->whereColumn('property_owners.ownerable_id', 'accounts.id')
                            ->where('property_owners.ownerable_type', Account::class)
                            ->where('properties.company_id', $user->company_id);
                    })
                    // OR include accounts that are lease members of leases owned by the company
                    ->orWhereExists(function ($subQuery) use ($user) {
                        $subQuery->select(DB::raw(1))
                            ->from('lease_members')
                            ->join('leases', 'lease_members.lease_id', '=', 'leases.id')
                            ->whereColumn('lease_members.member_id', 'accounts.id')
                            ->where('lease_members.memberable_type', Account::class)
                            ->where('leases.company_id', $user->company_id);
                    });
            })->distinct();
        }

        return $query->select('id', 'is_active', 'otp_activated_at', 'created_at', 'updated_at')->get();
    }

    private function calculateDailyStats($collectionData, $dateColumn): array
    {
        // Prepare daily counts
        $dailyStats = $collectionData->groupBy(function ($data) use ($dateColumn) {
            return $data->{$dateColumn}->format('Y-m-d');
        })->map(function ($dayData)  {
            return $dayData->count();
        });

        // Create date range for last 30 days
        $dates = collect(range(0, 29))->map(function ($days) {
            return now()->subDays($days)->format('Y-m-d');
        });

        // Map counts to dates
        return $dates->map(function ($date) use ($dailyStats) {
            return $dailyStats[$date] ?? 0;
        })->toArray();
    }

    protected function getStats(): array
    {
        // Use filtered accounts based on company access
        $accounts = $this->getFilteredAccounts();

        // Total Accounts (filtered by company access)
        $totalAccountsChart = $this->calculateDailyStats($accounts, 'created_at');
        $accountCount = $accounts->count();
        $this->cards[] = Stat::make(__('Total Accounts'), $accountCount)
            ->color('primary')
            ->chart(array_reverse($totalAccountsChart));

        // Active Accounts (filtered by company access)
        $activeAccountsData = $accounts->where('is_active', 1);
        $activeAccountsChart = $this->calculateDailyStats($activeAccountsData, 'updated_at');
        $activeAccounts = $activeAccountsData->count();
        $this->cards[] = Stat::make(__('Active Accounts'), $activeAccounts)
            ->color('success')
            ->chart(array_reverse($activeAccountsChart));

        // Inactive Accounts (filtered by company access)
        $inactiveAccountsData = $accounts->where('is_active', 0);
        $inactiveAccountsChart = $this->calculateDailyStats($inactiveAccountsData, 'updated_at');
        $inactiveAccounts = $inactiveAccountsData->count();
        $this->cards[] = Stat::make(__('Inactive Accounts'), $inactiveAccounts)
            ->color('danger')
            ->chart(array_reverse($inactiveAccountsChart));

        return $this->cards;
    }
}
