<?php

namespace Database\Seeders;

use App\Models\User;
use App\Enums\RoleEnum;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Modules\Company\app\Models\Company;
use Illuminate\Database\Eloquent\Collection;

class CloneRolesWithPermissionsForOldCompanies extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        try {
            DB::beginTransaction();
            Log::info('seeder started syncing data');

            $role      = $this->getRoleOwnerByName();
            $companies = Company::whereDoesntHave('roles')->get();

            $this->assignRolesPermissionsToRoleOwner($role);
            $this->cloneRolesWithPermissionsPerCompany($companies);

            DB::commit();
            Log::info('seeder finished syncing data');
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error('Failed to sync data', ['error' => $th]);
        }
    }

    protected function getRoleOwnerByName(): ?Role
    {
        return Role::where(['name' => RoleEnum::OWNER, 'guard_name' => 'web'])->first();
    }

    protected function assignRolesPermissionsToRoleOwner(Role $role): void
    {
        $permissions = ['view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role'];

        foreach($permissions as $permission){
            $permissionInstance = Permission::where(['name' => $permission, 'guard_name' => 'web'])->first();
            if($permissionInstance){
                $role->givePermissionTo($permissionInstance);
            }
        }
    }

    protected function cloneRolesWithPermissionsPerCompany(Collection $companies): void
    {
        if(!count($companies)){
            $this->command->info("No Companies Found To Clone Roles On");
        }

        $roles = $this->fetchOnlyNeededRoles();

        foreach ($companies as $company) {
            foreach ($roles as $role) {
                $this->syncPermissionsPerRole($role, $company);
            }
        }

        $this->command->info("Seeder Finished Cloning Roles On " .count($companies). " Companies");
    }

    protected function fetchOnlyNeededRoles()
    {
        return Role::whereNotIn('id', [1,4])->whereNull('company_id')->get();
    }

    protected function syncPermissionsPerRole(Role $originalRole, Company $company): void
    {
        if ($originalRole) {
            $uniqueStr      = Str::random(8);
            $user           = User::withoutGlobalScopes()->findOrFail($company->user_id);
            $roleUniqueName = $originalRole->name . '-V2-' . $uniqueStr;

            $clonedRole = Role::create([
                'name'            => $roleUniqueName,
                'company_id'      => $company?->id,
                'parent_id'       => $originalRole?->id,
                'translated_name' => json_encode([
                    'en' => $roleUniqueName,
                ]),
            ]);

            $permissions = $originalRole->permissions;
            $clonedRole->syncPermissions($permissions);
        }
    }
}
