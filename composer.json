{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "awcodes/light-switch": "^1.0", "barryvdh/laravel-dompdf": "^3.0", "bezhansalleh/filament-language-switch": "^3.1", "bezhansalleh/filament-shield": "^3.2", "cheesegrits/filament-google-maps": "^3.0", "clegginabox/pdf-merger": "dev-master", "coolsam/modules": "~4.0.0", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-translatable-plugin": "^3.2", "geniusts/hijri-dates": "^1.2", "guava/filament-icon-picker": "^2.0", "jaocero/radio-deck": "^1.2", "kreait/firebase-php": "^7.15", "laravel-notification-channels/fcm": "^4.3", "laravel/framework": "^11.9", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "maatwebsite/excel": "^3.1", "livewire/livewire": "3.5.18", "mpdf/mpdf": "^8.2", "owenvoke/blade-fontawesome": "^2.6", "parfaitementweb/filament-country-field": "^2.4", "rupadana/filament-swiper": "^3.0@beta", "spatie/laravel-query-builder": "^6.2", "spatie/laravel-settings": "^3.4", "spatie/laravel-translation-loader": "^2.7", "stechstudio/filament-impersonate": "^3.14", "stichoza/google-translate-php": "^5.2", "tapp/filament-value-range-filter": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Khaleds\\Shared\\": "core/khaleds/shared/src", "Khaleds\\Api\\": "core/khaleds/api/src", "Khaleds\\FilamentTranslations\\": "core/khaleds/Translation/FilamentTranslations/src", "Khaleds\\ConsoleHelpers\\": "core/khaleds/Translation/ConsoleHelpers/src", "Khaleds\\FilamentTranslationComponent\\": "core/khaleds/Translation/FilamentTranslationComponent/src", "Khaleds\\Notifications\\": "core/khaleds/Notifications/src"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}