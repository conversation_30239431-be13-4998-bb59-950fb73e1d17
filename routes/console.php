<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();


\Illuminate\Support\Facades\Schedule::command('subscription:expiration')->daily()->timezone('Asia/Riyadh');
\Illuminate\Support\Facades\Schedule::command('lease:status')->daily()->timezone('Asia/Riyadh');
\Illuminate\Support\Facades\Schedule::command('invoice:create-schedule')->daily()->timezone('Asia/Riyadh');
\Illuminate\Support\Facades\Schedule::command('finished-values')->everyMinute()->timezone('Asia/Riyadh');
\Illuminate\Support\Facades\Schedule::command('lease:renewal')->daily()->timezone('Asia/Riyadh');