<?php

namespace App\Observers;

use App\Models\User;
use App\Enums\RoleEnum;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Log;
use Modules\Company\app\Models\Company;
use Spatie\Permission\Models\Permission;

class CustomRolePermissionsPerCompanyObserver
{
     /**
     * Handle the Company "created" event.
     *
     * @param  Company $company
     * @return void
     */
    public function created(Company $company)
    {
        try {
            DB::beginTransaction();
            Log::info('observer started syncing data');
            $role = $this->getRoleOwnerByName();
            $this->assignRolesPermissionsToRoleOwner($role);
            $this->cloneRolesWithPermissionsPerCompany($company);
            DB::commit();
            Log::info('observer finished syncing data');
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error('Failed to sync data', ['error' => $th]);
        }
    }

    protected function getRoleOwnerByName(): ?Role
    {
        return Role::where(['name' => RoleEnum::OWNER, 'guard_name' => 'web'])->first();
    }

    protected function assignRolesPermissionsToRoleOwner(Role $role): void
    {
        $permissions = ['view_role', 'view_any_role', 'create_role', 'update_role', 'delete_role', 'delete_any_role'];

        foreach($permissions as $permission){
            $permissionInstance = Permission::where(['name' => $permission, 'guard_name' => 'web'])->first();
            if($permissionInstance){
                $role->givePermissionTo($permissionInstance);
            }
        }
    }

    protected function cloneRolesWithPermissionsPerCompany(Company $company): void
    {
        $roles = $this->fetchOnlyNeededRoles();
        foreach ($roles as $role) {
            $this->syncPermissionsPerRole($role, $company);
        }
    }

    protected function fetchOnlyNeededRoles()
    {
        return Role::whereNotIn('id', [1,4])->whereNull('company_id')->get();
    }

    protected function syncPermissionsPerRole(Role $originalRole, Company $company): void
    {
        if ($originalRole) {
            $uniqueStr      = Str::random(8);
            $user           = User::withoutGlobalScopes()->findOrFail($company->user_id);
            $roleUniqueName = $originalRole->name . '-V2-' . $uniqueStr;

            $clonedRole = Role::create([
                'name'            => $roleUniqueName,
                'company_id'      => $company?->id,
                'parent_id'       => $originalRole?->id,
                'translated_name' => json_encode([
                    'en' => $roleUniqueName,
                ]),
            ]);

            $permissions = $originalRole->permissions;
            $clonedRole->syncPermissions($permissions);
            // $user->assignRole($clonedRole);
        }
    }
}
