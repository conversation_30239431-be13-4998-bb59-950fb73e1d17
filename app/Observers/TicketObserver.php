<?php

namespace App\Observers;

use App\Enums\RoleEnum;
use App\Models\User;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Modules\Ticket\app\Models\Ticket;

class TicketObserver
{
    /**
     * Handle the Ticket "created" event.
     */
    public function created(Ticket $ticket): void
    {
        $template = NotificationsTemplate::where(['key' => 'new_ticket'])->first();
        if ($template) {
        $admins = User::role(RoleEnum::ADMIN)->get();

        foreach($admins as $admin)
        {
            SendNotification::make(['email'])
                ->template($template->key)
                ->model(User::class)
                ->id($admin->id)
                ->icon($template->icon)
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
        }
    }

    /**
     * Handle the Ticket "updated" event.
     */
    public function updated(Ticket $ticket): void
    {
        //
    }

    /**
     * Handle the Ticket "deleted" event.
     */
    public function deleted(Ticket $ticket): void
    {
        //
    }

    /**
     * Handle the Ticket "restored" event.
     */
    public function restored(Ticket $ticket): void
    {
        //
    }

    /**
     * Handle the Ticket "force deleted" event.
     */
    public function forceDeleted(Ticket $ticket): void
    {
        //
    }
}
