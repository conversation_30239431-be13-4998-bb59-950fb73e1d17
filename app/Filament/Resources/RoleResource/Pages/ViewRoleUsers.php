<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Models\User;
use Filament\Tables;
use Filament\Tables\Table;
use App\Services\RoleService;
use Filament\Resources\Pages\Page;
use Spatie\Permission\Models\Role;
use Filament\Tables\Contracts\HasTable;
use App\Filament\Resources\RoleResource;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Concerns\InteractsWithTable;

class ViewRoleUsers extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = RoleResource::class;

    protected static string $view = 'filament.resources.role-resource.pages.view-role-users';

    protected static ?string $title = 'Role Users';

    public Role $record;

    public static function canAccess(array $parameters = []): bool
    {
        return true;
    }

    public static function getRouteBaseName(?string $panel = null): string
    {
        return 'view-users';
    }

    public function mount(int | string $id): void
    {
        $this->record = Role::withoutGlobalScopes()->findOrFail($id);
        static::authorizeResourceAccess();
    }

    public function getTitle(): string
    {
        $label = $this->record->translated_name[app()->getLocale()] ?? $this->record->name;
        return __('Users with Role: :role', ['role' => $label]);
    }

    public function getBreadcrumbs(): array
    {
        return [
        ];
    }

    public function table(Table $table): Table
    {
        $children_roles = app(RoleService::class)->children($this->record)->pluck('id')->toArray();
        $roles_ids = array_merge([$this->record->id], $children_roles);

        return $table
            ->query(
                User::query()
                    ->whereHas('roles', function (Builder $query) use ($roles_ids) {
                        $query->whereIn('role_id', $roles_ids);
                    })
                    ->with(['roles', 'company']) // Add any relationships you need
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->label(__('Email'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('company.name')
                    ->label(__('Company'))
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('roles')
                    ->label(__('Other Roles'))
                    ->getStateUsing(function (User $record) {
                        return $record->roles()
                            ->where('id', '!=', $this->record->id)
                            ->pluck('translated_name')
                            ->implode(', ');
                    })
                    ->wrap()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Joined'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('company')
                    ->relationship('company', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('active_users')
                    ->label(__('Active Users'))
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('email_verified_at'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('View')
                    ->icon('heroicon-o-eye')
                    ->url(fn (User $record): string => route('filament.admin.resources.users.view', $record))
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('edit')
                    ->label('Edit')
                    ->icon('heroicon-o-pencil')
                    ->url(fn (User $record): string => route('filament.admin.resources.users.edit', $record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('remove_role_bulk')
                    ->label(__('Remove Role from Selected'))
                    ->icon('heroicon-o-minus-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading(__('Remove Role from Selected Users'))
                    ->modalDescription(__('Are you sure you want to remove the ":role" role from the selected users?', [
                        'role' => $this->record->translated_name ?? $this->record->name
                    ]))
                    ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                        foreach ($records as $user) {
                            $user->removeRole($this->record);
                        }

                        \Filament\Notifications\Notification::make()
                            ->success()
                            ->title(__('Role Removed'))
                            ->body(__('The role has been removed from :count users.', ['count' => $records->count()]))
                            ->send();
                    }),
            ])
            ->emptyStateHeading(__('No users found'))
            ->emptyStateDescription(__('This role has no users assigned to it.'))
            ->emptyStateIcon('heroicon-o-users');
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
}
