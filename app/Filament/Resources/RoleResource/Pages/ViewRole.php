<?php

namespace App\Filament\Resources\RoleResource\Pages;

use Filament\Actions;
use App\Enums\RoleEnum;
use App\Filament\Resources\RoleResource;
use Filament\Resources\Pages\ViewRecord;

class ViewRole extends ViewRecord
{
    protected static string $resource = RoleResource::class;

    public function getRecordTitle(): string
    {
        return RoleEnum::tryFrom($this->record->name)?->label() ?? $this->record->name;
    }

    public function getTitle(): string
    {
        return __('View Role :') . " " . RoleEnum::tryFrom($this->record->name)?->label();
    }
    protected function getActions(): array
    {
        return [
            Actions\EditAction::make()
            ->visible(function ($record) {
                $user = auth()->user();
                if ($user->hasRole(RoleEnum::ADMIN->value)) {
                    return !in_array($record->name, []);
                }else{
                    return !in_array($record->name, array_column(RoleEnum::cases(), 'value')) && ($record->company_id !== null && $record->company_id === $user?->company?->id);
                }
            }),
        ];
    }
}
