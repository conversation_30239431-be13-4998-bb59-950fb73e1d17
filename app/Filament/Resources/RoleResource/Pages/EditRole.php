<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Models\User;
use App\Enums\RoleEnum;
use Filament\Actions\Action;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Spatie\Permission\Models\Role;
use App\Events\Roles\RoleUpdatedEvent;
use App\Filament\Resources\RoleResource;
use Filament\Resources\Pages\EditRecord;
use BezhanSalleh\FilamentShield\Support\Utils;
use App\Shared\Components\CustomPageDeleteAction;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    public function getRecordTitle(): string
    {
        return RoleEnum::tryFrom($this->record->name)?->label() ?? $this->record->name;
    }

    public function getTitle(): string
    {
        return __('Edit Role :') . " " . RoleEnum::tryFrom($this->record->name)?->label();
    }

    public Collection $permissions;

    protected function getActions(): array
    {
        $user = auth()->user();

        if ($this->record->company_id !== $user?->company?->id) {
            abort(403);
        }

        return [
            Action::make('stickySave')
                ->label(__('save'))
                ->icon('heroicon-m-check')
                ->extraAttributes([
                    'class' => 'sticky-save-btn !fixed top-[50%] lg:top-[12%] ltr:right-[2%] rtl:left-[2%] z-[9]',
                ])
                ->action('save'), // ✅ run the same page action as the default Save button

            CustomPageDeleteAction::make()
                ->setRelationsList(['users'])
                /*->visible(function ($record) {
                    return !in_array($record->name, array_column(RoleEnum::cases(), 'value'));
                }),*/
                  ->visible(function ($record) {
                    $user = auth()->user();
                    if ($user->hasRole(RoleEnum::ADMIN->value)) {
                        return !in_array($record->name, array_column(RoleEnum::cases(), 'value'));
                    } else {
                        return !in_array($record->name, array_column(RoleEnum::cases(), 'value'))
                            && ($record->company_id === $user?->company?->id);
                    }
                }),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        return [
            ...$data,
            'translated_name' => [
                'en' => $data['translated_name']['en'] ?? '',
                'ar' => $data['translated_name']['ar'] ?? '',
            ],
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', 'companies', 'translated_name', 'is_default', 'is_editable', 'company_id']);
            })
            ->values()
            ->flatten()
            ->unique();

        // Format the translated_name data
        $translatedName = [
            'en' => $data['translated_name']['en'] ?? '',
            'ar' => $data['translated_name']['ar'] ?? '',
        ];

        return [
            'name' => $this->record->name, // Keep the original name
            'guard_name' => $data['guard_name'],
            'companies' => $data['companies'] ?? 0,
            'translated_name' => $translatedName,
            'is_default'      => $data['is_default'] ?? 0,
            'is_editable'     => $data['is_editable'] ?? 0,
        ];
    }

    protected function afterSave(): void
    {
        $permissionModels = collect();
        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                'name' => $permission,
                'guard_name' => $this->data['guard_name'],
            ]));
        });

        $this->record->syncPermissions($permissionModels);
        $company_ids = $this->record->users->pluck('company_id')->toArray();
        $this->syncRolesAddedPerCompany($company_ids, $permissionModels);
        event(new RoleUpdatedEvent($this->record));
    }

    protected function syncRolesAddedPerCompany(array $company_ids, $permissionModels)
    {
        $users = User::whereIn('company_id', $company_ids)->whereDoesntHave('companies')->get();

        foreach($users as $user){
            $role = $this->fetchUserRole($user);
            $this->syncPermissionsForEachRole($role, $permissionModels);
        }
    }

    protected function fetchUserRole(User $user): ?Role
    {
        return $user?->roles?->first();
    }

    protected function syncPermissionsForEachRole(?Role $role, $permissionModels): void
    {
        $role ? $role->syncPermissions($permissionModels) : '';
    }
}
