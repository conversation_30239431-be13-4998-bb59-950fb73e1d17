<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use <PERSON>zhanSalleh\FilamentShield\Support\Utils;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use App\Events\Roles\RoleCreatedEvent;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    public Collection $permissions;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', 'companies', 'translated_name']);
            })
            ->values()
            ->flatten()
            ->unique();

        // Format the translated_name data
        $translatedName = [
            'en' => $data['translated_name']['en'] ?? '',
            'ar' => $data['translated_name']['ar'] ?? '',
        ];

        // Generate the name slug from English translation
        $name = Str::slug($data['translated_name']['en']);

        return [
            'name' => $name,
            'guard_name' => $data['guard_name'],
            'companies' => $data['companies'] ?? 0,
            'translated_name' => $translatedName,
            'company_id'      => auth()->user()?->company_id,
            'is_default'      => $data['is_default'] ?? 0,
            'is_editable'     => $data['is_editable'] ?? 0,
        ];
    }

    protected function afterCreate(): void
    {
        $permissionModels = collect();
        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                /** @phpstan-ignore-next-line */
                'name' => $permission,
                'guard_name' => $this->data['guard_name'],
            ]));
        });

        $this->record->syncPermissions($permissionModels);

        // fire event that attach created role with companies
        // Fire event — listener will run in background
        event(new RoleCreatedEvent($this->record));

    }


    protected function getActions(): array
    {
        return [
            Action::make('stickySave')
                ->label(__('Create Role'))
                ->icon('heroicon-m-check')
                ->extraAttributes([
                    'class' => 'sticky-save-btn !fixed top-[50%] lg:top-[12%] ltr:right-[2%] rtl:left-[2%] z-[9]',
                ])
                ->action('create'), // ✅ run the same page action as the default Save button
        ];
    }

}
