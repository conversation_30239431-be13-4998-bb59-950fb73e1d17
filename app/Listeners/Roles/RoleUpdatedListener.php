<?php

namespace App\Listeners\Roles;

use Illuminate\Support\Facades\Log;
use App\Services\AssignRolesService;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\UpdateRoleAttributesService;

class RoleUpdatedListener implements ShouldQueue
{
    private UpdateRoleAttributesService $updateRoleAttributesService;

     /**
     * Create the event listener.
     */
    public function __construct(UpdateRoleAttributesService $updateRoleAttributesService)
    {
        $this->updateRoleAttributesService = $updateRoleAttributesService;
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        Log::info('queued event started syncing data');
        $role = $event->role;
        $this->updateRoleAttributesService->updateRoleAttributes($role);
        Log::info('queued event finished syncing data');
    }
}
