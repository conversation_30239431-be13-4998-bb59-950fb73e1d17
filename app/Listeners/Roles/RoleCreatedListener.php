<?php

namespace App\Listeners\Roles;

use Illuminate\Support\Facades\Log;
use App\Services\AssignRolesService;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class RoleCreatedListener implements ShouldQueue
{
    private AssignRolesService $assignRolesService;

     /**
     * Create the event listener.
     */
    public function __construct(AssignRolesService $assignRolesService)
    {
        $this->assignRolesService = $assignRolesService;
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        Log::info('queued event started syncing data');
        $role = $event->role;
        $this->assignRolesService->assignRoleWithPermissions($role);
        Log::info('queued event finished syncing data');
    }
}
