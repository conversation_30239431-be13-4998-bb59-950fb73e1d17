<?php

namespace App\Providers;

use App\Models\Document;
use App\Events\PasswordUpdated;
use App\Listeners\SyncPasswords;
use App\Listeners\SyncUserAccount;
use App\Observers\DocumentObserver;
use Illuminate\Support\Facades\Event;
use App\Events\Roles\RoleCreatedEvent;
use App\Events\Roles\RoleUpdatedEvent;
use Illuminate\Support\ServiceProvider;
use Modules\Company\app\Models\Company;
use App\Listeners\Roles\RoleCreatedListener;
use App\Listeners\Roles\RoleUpdatedListener;
use App\Providers\Filament\AdminPanelProvider;
use App\Providers\FilamentCacheServiceProvider;
use Modules\Subscription\app\Events\UserRegistered;
use Modules\Payment\app\Events\PaymentVerifiedEvent;
use BezhanSalleh\FilamentLanguageSwitch\LanguageSwitch;
use Modules\Subscription\app\Listeners\PaymentVerified;
use App\Observers\CustomRolePermissionsPerCompanyObserver;
use BezhanSalleh\FilamentShield\FilamentShieldServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        if(!$this->app->runningInConsole() || !$this->app->request->is('api/*')) {
            $this->app->register(AdminPanelProvider::class);
            $this->app->register(FilamentShieldServiceProvider::class);
            $this->app->register(FilamentCacheServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->loadViewsFrom(__DIR__.'/../../Modules/Invoice/resources/views/', 'invoice');
        $this->loadViewsFrom(__DIR__.'/../../Modules/Lease/resources/views/', 'lease');
        $this->loadViewsFrom(__DIR__.'/../../Modules/Property/resources/views/', 'property');
        $this->loadViewsFrom(__DIR__.'/../../Modules/Request/resources/views/', 'Request');
        $this->loadViewsFrom(__DIR__.'/../../Modules/EjarIntegration/resources/views/', 'ejarintegration');
        LanguageSwitch::configureUsing(function (LanguageSwitch $switch) {
            $switch
                ->locales(['ar','en']) // also accepts a closure
                ->outsidePanelRoutes(['login','register','password-reset'])
                ->visible(outsidePanels: true, insidePanels: false);

        });



        Event::listen(
            PaymentVerifiedEvent::class,
            PaymentVerified::class,
        );

        Event::listen(SyncUserAccountEvent::class, SyncUserAccount::class);
        Event::listen(PasswordUpdated::class, SyncPasswords::class);

        Event::listen(
            UserRegistered::class,
            \Modules\Subscription\app\Listeners\SetDefaultSubscription::class,
        );

        Document::observe(DocumentObserver::class);

        Event::listen(
            RoleCreatedEvent::class,
            RoleCreatedListener::class,
        );


        Event::listen(
            RoleUpdatedEvent::class,
            RoleUpdatedListener::class,
        );

        Company::observe(CustomRolePermissionsPerCompanyObserver::class);
    }
}
