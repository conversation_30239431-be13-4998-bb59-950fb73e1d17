<?php

namespace App\Shared\Components;

use App\Enums\RoleEnum;
use App\Services\RoleService;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Actions;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Resources\RoleResource;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Actions\Action as FormAction;

class RoleDeleteWithReassignAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'delete';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->name('delete');
        $this->label('Delete');
        $this->icon('heroicon-o-trash');
        $this->color('danger');
        $this->requiresConfirmation();

        $this->modalHeading(function (Model $record) {
            return (string) __('Delete Role: :name', ['name' => $record->translated_name ?? $record->name]);
        });

        $this->action(function (Model $record, array $data) {
            $user                       = auth()->user();
            $usersCount                 = $record->users()->count();
            $children_roles             = app(RoleService::class)->children($record);
            $children_roles_count_users = $children_roles->withCount('users')->get();
            $total_user_count           = $children_roles_count_users->sum('users_count');

            if ($user->hasRole(RoleEnum::ADMIN) && ($children_roles->count() > 0 || $total_user_count > 0)) {
                Notification::make()
                    ->danger()
                    ->title(__('Cannot Delete Role'))
                    ->body(__('This role has child roles or those child roles have users assigned, so it cannot be deleted.'))
                    ->send();
                return false; // Stop execution if conditions are met
            }

            if ($usersCount > 0) {
                if (!isset($data['new_role_id'])) {
                    // This shouldn't happen since we set up the form, but just in case
                    Notification::make()
                        ->danger()
                        ->title(__('Error'))
                        ->body(__('Please select a role to reassign users to.'))
                        ->send();
                    return;
                }

                $this->reassignUsersAndDelete($record, $data['new_role_id'], $usersCount);
            } else {
                // No users attached, proceed with normal deletion
                $record->delete();

                Notification::make()
                    ->success()
                    ->title((string) __('Role Deleted Successfully'))
                    ->send();
            }
        });

        // Set up the form conditionally based on whether users are attached
        $this->form(function (Model $record) {

            $usersCount                 = $record->users()->count();
            $children_roles             = app(RoleService::class)->children($record);
            $children_roles_count_users = $children_roles->withCount('users')->get();
            $total_user_count           = $children_roles_count_users->sum('users_count');
            $count_children             = $children_roles->count();
            $actual_count = $record->parent_id == null ? $usersCount + $total_user_count : $usersCount;

            if ($usersCount === 0 && $total_user_count === 0) {

                // No users attached, show simple confirmation
                return [
                    Placeholder::make('confirmation')
                        ->content(__('Are you sure you want to delete this role?'))
                ];
            }

            $count_message = $usersCount > 0 ?
                __('This role has :count users attached and there are no other roles available for reassignment. Cannot delete this role.', ['count' => $usersCount]) :
                __('This role\'s children has :count users attached and there are no other roles available for reassignment. Cannot delete this role.', ['count' => $total_user_count]);

            // Users are attached, show reassignment form
            $availableRoles = $this->getAvailableRoles($record);

            if (empty($availableRoles)) {
                return [Placeholder::make('error')->content($count_message)];
            }

            return [
                Placeholder::make('warning')
                    ->content((string) $count_message),

                Select::make('new_role_id')
                    ->label((string) __('Reassign users to'))
                    ->options($availableRoles)
                    ->required()
                    ->searchable()
                    ->placeholder((string) __('Select a role'))
                    ->visible(function ($record) use ($total_user_count, $count_children) {
                        $user = auth()->user();
                        if ($user->hasRole(RoleEnum::ADMIN->value)) {
                            return ($user?->hasRole(RoleEnum::ADMIN) && $count_children == 0 && $record->parent_id == null);
                        } else {
                            return ($record->company_id === $user?->company?->id);
                        }
                    }),

                Actions::make([
                    FormAction::make('viewUsers')
                        ->label((string) __('View :count Users', ['count' => $actual_count]))
                        ->color('info')
                        ->icon('heroicon-o-users')
                        ->url($this->getUsersListUrl($record))
                        ->openUrlInNewTab(),
                ])->fullWidth(),
            ];
        });

        $this->modalSubmitActionLabel(function (Model $record) {
            $user                       = auth()->user();
            $usersCount                 = $record->users()->count();
            $children_roles             = app(RoleService::class)->children($record);
            $children_roles_count_users = $children_roles->withCount('users')->get();
            $total_user_count           = $children_roles_count_users->sum('users_count');

            if ($user->hasRole(RoleEnum::ADMIN) && ($children_roles->count() > 0 || $total_user_count > 0)) {
                Notification::make()
                    ->danger()
                    ->title(__('Cannot Delete Role'))
                    ->body(__('This role has child roles or those child roles have users assigned, so it cannot be deleted.'))
                    ->send();
            } else {
                return (string) ($record->users()->count() > 0 ? 'Reassign & Delete' : 'Delete');
            }
        });

        $this->modalCancelActionLabel((string) __('Cancel'));

        // Disable the action if users exist but no roles are available for reassignment
        $this->disabled(function (Model $record) {

            $usersCount = $record->users()->count();
            if ($usersCount > 0) {
                $availableRoles = $this->getAvailableRoles($record);
                // dd('');
                return empty($availableRoles);
            }
            return false;
        });
    }


    protected function getAvailableRoles(Model $roleToDelete): array
    {
       // Use the same eloquent query method as RoleResource
        $query = RoleResource::getEloquentQuery();

        // Exclude the role being deleted
        $query->where('id', '!=', $roleToDelete->id);

        // Get roles and format for select options
        return $query->get()->mapWithKeys(function ($role) {
            $label = $role->translated_name[app()->getLocale()] ?? $role->name;
            if (RoleEnum::tryFrom($role->name)) {
                $label = RoleEnum::tryFrom($role->name)->label();
            }
            return [$role->id => $label];
        })->toArray();
    }

    protected function getUsersListUrl(Model $record): string
    {
        return RoleResource::getUrl('view-users', ['id' => $record->id]);
    }

    protected function reassignUsersAndDelete(Model $record, int $newRoleId, int $usersCount): void
    {
        try {
            DB::beginTransaction();

            // Get the new role
            $newRole = Role::withoutGlobalScopes()->findOrFail($newRoleId);

            // Update old role with new one
            DB::table('model_has_roles')
                ->where('role_id', $record->id)
                ->update(['role_id' => $newRole->id]);

            // Delete the role
            $record->delete();

            DB::commit();

            Notification::make()
                ->success()
                ->title((string) __('Role Deleted Successfully'))
                ->body((string) __(':count users have been reassigned to ":newRole" and the role has been deleted.', [
                    'count' => $usersCount,
                    'newRole' => $newRole->translated_name ?? $newRole->name
                ]))
                ->send();

        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->danger()
                ->title((string) __('Error'))
                ->body((string) __('An error occurred while reassigning users and deleting the role: :error', ['error' => $e->getMessage()]))
                ->send();
        }
    }
}
