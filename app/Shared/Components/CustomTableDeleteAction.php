<?php

namespace App\Shared\Components;

use App\Services\RoleService;
use Filament\Tables\Actions\DeleteAction;

class CustomTableDeleteAction extends DeleteAction
{
    public array $relationsList = [];

    public function setRelationsList(array $relationsList): static
    {
        $this->relationsList = $relationsList;

        return $this;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->action(function ($record) {

            $hasRelations = false;

            foreach ($this->relationsList as $relation) {
                if ( !$record->$relation()->get()->isEmpty() ) {
                    $hasRelations = true;
                    if($hasRelations) break;
                }
            }

            if ($hasRelations) {
                $company_ids = count($record->users->pluck('company_id')->toArray());
                \Filament\Notifications\Notification::make()
                    ->danger()
                    ->title(__('Cannot Delete'))
                    ->body(__('This record has :count related user(s). Please remove all related records first.', ['count' => $company_ids]))
                    ->send();
                return;
            }

            $children_roles             = app(RoleService::class)->children($record);
            $children_roles_count_users = $children_roles->withCount('users')->get();
            $total_user_count           = $children_roles_count_users->sum('users_count');
            $count_children             = $children_roles->count();

            if ($total_user_count > 0) {

                \Filament\Notifications\Notification::make()
                    ->danger()
                    ->title(__('Cannot Delete'))
                    ->body(__('This record related roles has :count related user(s). Please remove all related records first.', ['count' => $total_user_count]))
                    ->send();
                return;
            }

            if ($count_children > 0) {
                \Filament\Notifications\Notification::make()
                    ->danger()
                    ->title(__('Cannot Delete'))
                    ->body(__('This record has :count related role(s). Please remove all related records first.', ['count' => $count_children]))
                    ->send();
                return;
            }

            $record->delete();
            \Filament\Notifications\Notification::make()
                ->success()
                ->title(__('Deleted'))
                ->body(__('The record has been deleted.'))
                ->send();
        });
    }
}
