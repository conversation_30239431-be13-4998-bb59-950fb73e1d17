<?php

namespace App\Services;

use App\Models\User;
use App\Enums\RoleEnum;
use Spatie\Permission\Models\Role;
use Modules\Lease\app\Models\Lease;
use Illuminate\Support\Facades\Schema;
use Modules\Company\app\Models\Company;
use Illuminate\Database\Eloquent\Builder;
use Modules\Property\app\Models\Property;

class RoleService
{
    public function applyUserRoleRestrictions(Builder $query): Builder
    {
        $user = auth()->user();

        if ($user && !$user->hasRole(RoleEnum::ADMIN)) {

            if ($user && $user->hasRole(RoleEnum::OWNER)) {
                //$query->whereNotIN('id', [1,4])->where('company_id', $user?->company?->id);
                $query->where(function ($query) use ($user) {
                    // The first condition: exclude IDs 1 and 4 and match the user's company ID
                    $query->where('company_id', $user->company->id)
                        // The second condition: include records where companies is 1 and company_id is null
                        ->orWhere(function ($query) {
                            $query->where('companies', 1)->whereNull('company_id');
                        })
                        ->whereNotIn('id', [1, 4]);
                });
            }else{
                $query->where('company_id', $user?->company?->id)->whereNotIN('name', $user->getRoleNames());
            }
        }

        $data = $query; //$query->whereNull('parent_id')->whereNull('company_id');
        return $data;
    }

    public function fetchRolesDropdown(User $user, Builder $query): Builder
    {
        if ($user && !$user->hasRole(RoleEnum::ADMIN)) {

            if ($user && $user->hasRole(RoleEnum::OWNER)) {
                $query->where(function ($query) use ($user) {
                    // The first condition: exclude IDs 1 and 4 and match the user's company ID
                    $query->where('company_id', $user->company->id)
                        // The second condition: include records where companies is 1 and company_id is null
                        ->orWhere(function ($query) {
                            $query->where('companies', 1)->whereNull('company_id');
                        })
                        ->whereNotIn('id', [1, 4]);
                });
            }else{
                $query->where(function ($query) use ($user) {
                    // The first condition: exclude IDs 1 and 4 and match the user's company ID
                    $query->where('company_id', $user->company->id)
                        // The second condition: include records where companies is 1 and company_id is null
                        ->orWhere(function ($query) {
                            $query->where('companies', 1)->whereNull('company_id');
                        })
                        ->whereNotIn('id', [1, 4])
                        ->whereNotIn('name', $user->getRoleNames());
                });
            }
        }

        return $query;
    }

    public function applyBrokerRoleRestrictions(Builder $query,$column = 'broker_id',$relation = null): Builder
    {
        if (auth()->user()->hasRole(RoleEnum::BROKER)) {
            if($relation){
                $query->whereHas($relation, function ($subQuery) use ($column) {
                    if(get_class($subQuery->getModel()) == Lease::class){
                        $subQuery->with('property',function($q) use ($column){
                            $q->where($column, auth()->id());
                        });
                    }else{
                        $subQuery->where($column, auth()->id());
                    }
                });
            }else{
               $query->where($column, auth()->id());
            }
        }
        return $query;
    }

    public function children(Role $role)
    {
        return Role::where('parent_id', $role->id);
    }

    public function getCompanyNameById(?int $companyId): string
    {
        return Company::find($companyId)?->name ?? '—';
    }
}
