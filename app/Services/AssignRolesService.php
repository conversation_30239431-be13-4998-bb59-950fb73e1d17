<?php

namespace App\Services;

use App\Enums\RoleEnum;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Log;
use Modules\Company\app\Models\Company;

class AssignRolesService
{
    public function assignRoleWithPermissions(Role $originalRole)
    {
        // Handle cases here for values of (is_editable, is_default)

        // CASE #1
        if ($originalRole->is_editable == 0 && $originalRole->is_default == 0) {
            // Logic for case #1
            $originalRole->companies = 0;
            $originalRole->save();
        }

        // CASE #2
        if ($originalRole->is_editable == 0 && $originalRole->is_default == 1) {
            // Logic for case #2
            $originalRole->companies = 1;
            $originalRole->save();
        }

        // CASE #3
        if ($originalRole->is_editable == 1 && $originalRole->is_default == 0) {
            // Logic for case #3
            Company::whereHas('user', function($q){
                $q->where('active', 1);
            })
            ->chunk(100, function ($companies) use ($originalRole) {
                foreach ($companies as $company) {
                    $roleUniqueName = $originalRole->name . '-V2-' . Str::random(8);
                    $clonedRole     = Role::create([
                        'name'            => $roleUniqueName,
                        'company_id'      => $company?->id,
                        'parent_id'       => $originalRole?->id,
                        'translated_name' => json_encode([
                            'en' => $roleUniqueName,
                        ]),
                    ]);

                    $permissions = $originalRole->permissions;
                    $clonedRole->syncPermissions($permissions);
                }
            });

            $originalRole->companies = 0;
            $originalRole->save();
        }

        // CASE #4
        if ($originalRole->is_editable == 1 && $originalRole->is_default == 1) {
            // Logic for case #4
            Company::whereHas('user', function($q){
                $q->where('active', 1);
            })
            ->chunk(100, function ($companies) use ($originalRole) {
                foreach ($companies as $company) {
                    $roleUniqueName = $originalRole->name . '-V2-' . Str::random(8);
                    $clonedRole     = Role::create([
                        'name'            => $roleUniqueName,
                        'company_id'      => $company?->id,
                        'parent_id'       => $originalRole?->id,
                        'translated_name' => json_encode([
                            'en' => $roleUniqueName,
                        ]),
                    ]);

                    $permissions = $originalRole->permissions;
                    $clonedRole->syncPermissions($permissions);
                }
            });

            $originalRole->companies = 1;
            $originalRole->save();
        }

    }
}
